<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCQ & TF Question Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <button id="headerBackBtn" class="btn btn-icon" title="Back to Main" style="display: none; margin-right: 10px;">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-graduation-cap"></i>
                    <h1 data-translate="appTitle">MCQ & TF Generator</h1>
                </div>
                <div class="header-actions">
                    <button id="languageToggle" class="btn btn-icon" title="Switch Language">
                        <span class="language-text">عربي</span>
                    </button>



                    <button id="historyBtn" class="btn btn-icon" title="History">
                        <i class="fas fa-history"></i>
                    </button>
                    <button id="statsBtn" class="btn btn-icon" title="Statistics">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Home Screen - Service Launcher -->
            <div id="homeScreen" class="screen active">
                <div class="home-container">
                    <!-- Header -->
                    <div class="home-header">
                        <div class="app-branding">
                            <i class="fas fa-cube app-icon"></i>
                            <div class="app-info">
                                <h1 class="app-title" data-translate="appMainTitle">Multi-Service Desktop Suite</h1>
                                <p class="app-subtitle" data-translate="appMainSubtitle">Your all-in-one productivity toolkit</p>
                            </div>
                        </div>
                        <div class="home-actions">
                            <button id="homeLanguageToggle" class="btn btn-icon" title="Switch Language">
                                <span class="language-text">عربي</span>
                            </button>
                            <div class="user-info">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span class="user-name" data-translate="desktopUser">Desktop User</span>
                            </div>
                        </div>
                    </div>

                    <!-- Services Grid -->
                    <div class="services-grid">
                        <!-- Quiz Generator Service -->
                        <div class="service-card quiz-service" id="quizGeneratorService">
                            <div class="service-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="quizGenerator">Quiz Generator</h3>
                                <p class="service-description" data-translate="quizGeneratorDesc">Generate MCQ & True/False questions from any content</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="aiPowered">AI Powered</span>
                                    <span class="feature-tag" data-translate="multipleFormats">Multiple Formats</span>
                                    <span class="feature-tag" data-translate="interactive">Interactive</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn">
                                    <i class="fas fa-arrow-right"></i>
                                    <span data-translate="launch">Launch</span>
                                </button>
                            </div>
                        </div>

                        <!-- Image to PDF Converter Service -->
                        <div class="service-card pdf-service" id="imageToPdfService">
                            <div class="service-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="imageToPdf">Image to PDF</h3>
                                <p class="service-description" data-translate="imageToPdfDesc">Convert images to PDF documents with advanced options</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="batchConvert">Batch Convert</span>
                                    <span class="feature-tag" data-translate="highQuality">High Quality</span>
                                    <span class="feature-tag" data-translate="customSettings">Custom Settings</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn">
                                    <i class="fas fa-arrow-right"></i>
                                    <span data-translate="launch">Launch</span>
                                </button>
                            </div>
                        </div>

                        <!-- Document Converter Service -->
                        <div class="service-card doc-service" id="documentConverterService">
                            <div class="service-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="documentConverter">Document Converter</h3>
                                <p class="service-description" data-translate="documentConverterDesc">Convert between various document formats</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="multiFormat">Multi-Format</span>
                                    <span class="feature-tag" data-translate="batchProcess">Batch Process</span>
                                    <span class="feature-tag" data-translate="comingSoon">Coming Soon</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn" disabled>
                                    <i class="fas fa-clock"></i>
                                    <span data-translate="soon">Soon</span>
                                </button>
                            </div>
                        </div>

                        <!-- Text Tools Service -->
                        <div class="service-card text-service" id="textToolsService">
                            <div class="service-icon">
                                <i class="fas fa-text-width"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="textTools">Text Tools</h3>
                                <p class="service-description" data-translate="textToolsDesc">Advanced text processing and analysis tools</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="ocr">OCR</span>
                                    <span class="feature-tag" data-translate="analysis">Analysis</span>
                                    <span class="feature-tag" data-translate="comingSoon">Coming Soon</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn" disabled>
                                    <i class="fas fa-clock"></i>
                                    <span data-translate="soon">Soon</span>
                                </button>
                            </div>
                        </div>

                        <!-- Study Tools Service -->
                        <div class="service-card study-service" id="studyToolsService">
                            <div class="service-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="studyTools">Study Tools</h3>
                                <p class="service-description" data-translate="studyToolsDesc">Flashcards, spaced repetition, and study planning</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="spacedRepetition">Spaced Repetition</span>
                                    <span class="feature-tag" data-translate="analytics">Analytics</span>
                                    <span class="feature-tag" data-translate="comingSoon">Coming Soon</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn" disabled>
                                    <i class="fas fa-clock"></i>
                                    <span data-translate="soon">Soon</span>
                                </button>
                            </div>
                        </div>

                        <!-- AI Assistant Service -->
                        <div class="service-card ai-service" id="aiAssistantService">
                            <div class="service-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="service-content">
                                <h3 class="service-title" data-translate="aiAssistant">AI Assistant</h3>
                                <p class="service-description" data-translate="aiAssistantDesc">Chat with AI for help, explanations, and more</p>
                                <div class="service-features">
                                    <span class="feature-tag" data-translate="multiModel">Multi-Model</span>
                                    <span class="feature-tag" data-translate="contextAware">Context Aware</span>
                                    <span class="feature-tag" data-translate="comingSoon">Coming Soon</span>
                                </div>
                            </div>
                            <div class="service-action">
                                <button class="service-btn" disabled>
                                    <i class="fas fa-clock"></i>
                                    <span data-translate="soon">Soon</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="quick-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">0</span>
                                <span class="stat-label" data-translate="questionsGenerated">Questions Generated</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">0</span>
                                <span class="stat-label" data-translate="filesProcessed">Files Processed</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">0h</span>
                                <span class="stat-label" data-translate="timeSaved">Time Saved</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quiz Generator Screen (formerly Welcome Screen) -->
            <div id="quizGeneratorScreen" class="screen">
                <div class="welcome-layout">
                    <!-- Main Content Area -->
                    <div class="welcome-main-content">
                        <div class="welcome-header">
                            <i class="fas fa-brain welcome-icon"></i>
                            <h2 data-translate="welcomeTitle">Welcome to Question Generator</h2>
                            <p data-translate="welcomeSubtitle">Generate multiple-choice and true/false questions from your educational content</p>
                        </div>

                        <div class="question-type-selection">
                            <h3 data-translate="chooseQuestionType">Choose Question Type</h3>
                            <div class="type-buttons">
                                <button id="mcqBtn" class="type-btn mcq-btn">
                                    <i class="fas fa-list-ul"></i>
                                    <span data-translate="multipleChoice">Multiple Choice (MCQ)</span>
                                    <small data-translate="multipleChoiceDesc">Generate questions with multiple options</small>
                                </button>
                                <button id="tfBtn" class="type-btn tf-btn">
                                    <i class="fas fa-check-circle"></i>
                                    <span data-translate="trueFalse">True/False (TF)</span>
                                    <small data-translate="trueFalseDesc">Generate true or false questions</small>
                                </button>
                            </div>
                        </div>

                        <div class="input-methods">
                            <h3 data-translate="addYourContent">Add Your Content</h3>
                            <div class="input-options">
                                <div class="input-option">
                                    <button id="textInputBtn" class="input-btn">
                                        <i class="fas fa-keyboard"></i>
                                        <span data-translate="typeText">Type Text</span>
                                    </button>
                                </div>
                                <div class="input-option">
                                    <button id="fileUploadBtn" class="input-btn">
                                        <i class="fas fa-file-upload"></i>
                                        <span data-translate="uploadFile">Upload File</span>
                                    </button>
                                </div>
                                <div class="input-option">
                                    <button id="imageUploadBtn" class="input-btn">
                                        <i class="fas fa-image"></i>
                                        <span data-translate="uploadImage">Upload Image</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="welcome-sidebar">
                        <!-- Question Count Settings -->
                        <div class="sidebar-card question-settings-card">
                            <div class="card-header">
                                <div class="header-icon">
                                    <i class="fas fa-sliders-h"></i>
                                </div>
                                <div class="header-content">
                                    <h3 class="card-title" data-translate="questionCountSettings">Question Count Settings</h3>
                                    <p class="card-subtitle" data-translate="questionCountSettingsDesc">Configure how many questions to generate</p>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="settings-grid-sidebar">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-icon">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <div class="setting-text">
                                                <label for="fileQuestionsCount" class="setting-title" data-translate="questionsPerPage">Questions per Page</label>
                                                <p class="setting-description" data-translate="questionsPerPageDesc">Questions per page</p>
                                            </div>
                                        </div>
                                        <div class="setting-control">
                                            <input type="number" id="fileQuestionsCount" min="1" max="10" value="2" class="number-input">
                                        </div>
                                    </div>

                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <div class="setting-icon">
                                                <i class="fas fa-image"></i>
                                            </div>
                                            <div class="setting-text">
                                                <label for="imageQuestionsCount" class="setting-title" data-translate="questionsPerImage">Questions per Image</label>
                                                <p class="setting-description" data-translate="questionsPerImageDesc">Questions per image</p>
                                            </div>
                                        </div>
                                        <div class="setting-control">
                                            <input type="number" id="imageQuestionsCount" min="1" max="15" value="5" class="number-input">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Model Selection -->
                        <div class="sidebar-card model-selection-card">
                            <div class="card-header">
                                <div class="header-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="header-content">
                                    <h3 class="card-title" data-translate="aiModelSelection">AI Model Selection</h3>
                                    <p class="card-subtitle" data-translate="aiModelSelectionDesc">Choose your preferred AI model for question generation</p>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="model-selector-container">
                                    <div class="selector-wrapper">
                                        <label for="modelSelect" class="selector-label">
                                            <i class="fas fa-robot"></i>
                                            <span data-translate="preferredAiModel">Preferred AI Model</span>
                                        </label>
                                        <div class="custom-select-wrapper">
                                            <select id="modelSelect" class="modern-select">
                                                <option value="auto" data-translate="autoBestAvailable">Auto (Best Available)</option>
                                                <!-- Models will be loaded dynamically from backend -->
                                            </select>
                                            <div class="select-arrow">
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="model-status-card" id="modelStatus">
                                        <div class="status-indicator-wrapper">
                                            <span class="status-indicator status-unknown"></span>
                                            <span class="status-text" data-translate="checkingAvailability">Checking availability...</span>
                                        </div>
                                        <div class="status-details">
                                            <span class="status-description" data-translate="modelStatusWillAppear">Model status will appear here</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Model Management -->
                        <div class="sidebar-card model-management-card">
                            <div class="card-header">
                                <div class="header-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="header-content">
                                    <h3 class="card-title" data-translate="modelManagement">Model Management</h3>
                                    <p class="card-subtitle" data-translate="modelManagementDesc">Manage your AI models and API configuration</p>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="management-grid-sidebar">
                                    <div class="management-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-robot"></i>
                                            <span data-translate="aiModels">AI Models</span>
                                        </h4>
                                        <div class="action-buttons-grid-sidebar">
                                            <button id="addModelBtn" class="sidebar-btn btn-add">
                                                <div class="btn-icon">
                                                    <i class="fas fa-plus"></i>
                                                </div>
                                                <div class="btn-content">
                                                    <span class="btn-title" data-translate="addModel">Add Model</span>
                                                    <span class="btn-subtitle" data-translate="addNewAiModel">Add new AI model</span>
                                                </div>
                                            </button>

                                            <button id="removeModelBtn" class="sidebar-btn btn-remove">
                                                <div class="btn-icon">
                                                    <i class="fas fa-trash"></i>
                                                </div>
                                                <div class="btn-content">
                                                    <span class="btn-title" data-translate="removeModel">Remove Model</span>
                                                    <span class="btn-subtitle" data-translate="deleteExistingModel">Delete existing model</span>
                                                </div>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="management-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-tools"></i>
                                            <span data-translate="toolsTesting">Tools & Testing</span>
                                        </h4>
                                        <div class="action-buttons-grid-sidebar">
                                            <button id="testModelsBtn" class="sidebar-btn btn-test">
                                                <div class="btn-icon">
                                                    <i class="fas fa-flask"></i>
                                                </div>
                                                <div class="btn-content">
                                                    <span class="btn-title" data-translate="testModels">Test Models</span>
                                                    <span class="btn-subtitle" data-translate="verifyFunctionality">Verify functionality</span>
                                                </div>
                                            </button>

                                            <button id="manageApiKeyBtn" class="sidebar-btn btn-api">
                                                <div class="btn-icon">
                                                    <i class="fas fa-key"></i>
                                                </div>
                                                <div class="btn-content">
                                                    <span class="btn-title" data-translate="apiKey">API Key</span>
                                                    <span class="btn-subtitle" data-translate="manageCredentials">Manage credentials</span>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Image to PDF Converter Screen -->
            <div id="imageToPdfScreen" class="screen">
                <div class="pdf-converter-container">
                    <div class="screen-header">
                        <button id="backToHomeFromPdf" class="btn btn-secondary">
                            <i class="fas fa-home"></i> <span data-translate="home">Home</span>
                        </button>
                        <h2 data-translate="imageToPdf">Image to PDF Converter</h2>
                    </div>

                    <div class="pdf-converter-layout">
                        <!-- Main Upload Area -->
                        <div class="upload-section">
                            <div class="upload-zone" id="pdfImageDropZone">
                                <div class="upload-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="upload-content">
                                    <h3 data-translate="dragDropImages">اسحب وأفلت الصور هنا</h3>
                                    <p data-translate="orClickToSelect">أو انقر لاختيار الملفات</p>
                                </div>
                                <div class="supported-formats">
                                    <span data-translate="supportedFormats">التنسيقات المدعومة:</span>
                                    <div class="format-tags">
                                        <span class="format-tag">JPG</span>
                                        <span class="format-tag">PNG</span>
                                        <span class="format-tag">BMP</span>
                                        <span class="format-tag">TIFF</span>
                                    </div>
                                </div>
                                <input type="file" id="pdfImageInput" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" multiple hidden>
                            </div>

                            <!-- Selected Images Preview -->
                            <div id="selectedImagesContainer" class="selected-images hidden">
                                <div class="section-header">
                                    <h4 data-translate="selectedImages">Selected Images</h4>
                                    <button id="clearAllImages" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> <span data-translate="clearAll">Clear All</span>
                                    </button>
                                </div>
                                <div id="imagesList" class="images-list"></div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="pdf-settings-sidebar">
                            <!-- PDF Settings Card -->
                            <div class="settings-card">
                                <div class="card-header">
                                    <div class="header-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="header-content">
                                        <h3 class="card-title" data-translate="pdfSettings">PDF Settings</h3>
                                        <p class="card-subtitle" data-translate="pdfSettingsDesc">Configure your PDF output</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="setting-group">
                                        <label for="pdfQuality" class="setting-label">
                                            <i class="fas fa-image"></i>
                                            <span data-translate="imageQuality">Image Quality</span>
                                        </label>
                                        <select id="pdfQuality" class="setting-select">
                                            <option value="high" data-translate="highQuality">High Quality</option>
                                            <option value="medium" data-translate="mediumQuality" selected>Medium Quality</option>
                                            <option value="low" data-translate="lowQuality">Low Quality</option>
                                        </select>
                                    </div>

                                    <div class="setting-group">
                                        <label for="pageSize" class="setting-label">
                                            <i class="fas fa-file-alt"></i>
                                            <span data-translate="pageSize">Page Size</span>
                                        </label>
                                        <select id="pageSize" class="setting-select">
                                            <option value="a4" data-translate="a4Size" selected>A4</option>
                                            <option value="letter" data-translate="letterSize">Letter</option>
                                            <option value="legal" data-translate="legalSize">Legal</option>
                                            <option value="auto" data-translate="autoSize">Auto (Fit Image)</option>
                                        </select>
                                    </div>

                                    <div class="setting-group">
                                        <label for="orientation" class="setting-label">
                                            <i class="fas fa-rotate-90"></i>
                                            <span data-translate="orientation">Orientation</span>
                                        </label>
                                        <select id="orientation" class="setting-select">
                                            <option value="auto" data-translate="autoOrientation" selected>Auto</option>
                                            <option value="portrait" data-translate="portrait">Portrait</option>
                                            <option value="landscape" data-translate="landscape">Landscape</option>
                                        </select>
                                    </div>

                                    <div class="setting-group">
                                        <label for="margin" class="setting-label">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                            <span data-translate="margins">Margins</span>
                                        </label>
                                        <select id="margin" class="setting-select">
                                            <option value="none" data-translate="noMargins">No Margins</option>
                                            <option value="small" data-translate="smallMargins" selected>Small</option>
                                            <option value="medium" data-translate="mediumMargins">Medium</option>
                                            <option value="large" data-translate="largeMargins">Large</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Conversion Actions -->
                            <div class="actions-card">
                                <div class="card-header">
                                    <div class="header-icon">
                                        <i class="fas fa-magic"></i>
                                    </div>
                                    <div class="header-content">
                                        <h3 class="card-title" data-translate="convertToPdf">Convert to PDF</h3>
                                        <p class="card-subtitle" data-translate="convertToPdfDesc">Generate your PDF document</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <button id="convertToPdfBtn" class="btn btn-primary btn-large" disabled>
                                        <i class="fas fa-file-pdf"></i>
                                        <span data-translate="generatePdf">Generate PDF</span>
                                    </button>
                                    <div class="conversion-info">
                                        <div class="info-item">
                                            <span data-translate="imagesSelected">Images Selected:</span>
                                            <span id="imageCount">0</span>
                                        </div>
                                        <div class="info-item">
                                            <span data-translate="estimatedSize">Estimated Size:</span>
                                            <span id="estimatedSize">0 MB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Input Screen -->
            <div id="contentScreen" class="screen">
                <div class="content-container">
                    <div class="screen-header">
                        <button id="backToQuizGenerator" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                        <button id="backToHome" class="btn btn-secondary">
                            <i class="fas fa-home"></i> <span data-translate="home">Home</span>
                        </button>
                        <h2 id="contentScreenTitle" data-translate="addContent">Add Content</h2>
                    </div>

                    <!-- Text Input -->
                    <div id="textInputArea" class="input-area">
                        <textarea id="textContent" data-translate="enterContentPlaceholder" placeholder="Enter your educational content here..." rows="10"></textarea>
                        <div class="input-actions">
                            <button id="generateFromText" class="btn btn-primary">
                                <i class="fas fa-magic"></i> <span data-translate="generateQuestions">Generate Questions</span>
                            </button>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div id="fileUploadArea" class="input-area">
                        <div class="upload-zone" id="fileDropZone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p data-translate="dragDropFile">Drag and drop your file here or click to browse</p>
                            <small data-translate="supportedFiles">Supported: PDF, DOCX, DOC, TXT</small>
                            <input type="file" id="fileInput" accept=".pdf,.docx,.doc,.txt" hidden>
                        </div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="file-details">
                                <i class="fas fa-file"></i>
                                <span id="fileName"></span>
                                <span id="fileSize"></span>
                            </div>
                            <button id="removeFile" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromFile" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> <span data-translate="generateQuestions">Generate Questions</span>
                            </button>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div id="imageUploadArea" class="input-area">
                        <div class="upload-zone" id="imageDropZone">
                            <i class="fas fa-image"></i>
                            <p data-translate="dragDropImage">Drag and drop your image here or click to browse</p>
                            <small data-translate="supportedImages">Supported: JPG, PNG, BMP, TIFF</small>
                            <input type="file" id="imageInput" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" hidden>
                        </div>
                        <div id="imagePreview" class="image-preview hidden">
                            <img id="previewImg" src="" alt="Preview">
                            <button id="removeImage" class="btn btn-danger btn-sm">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="input-actions">
                            <button id="generateFromImage" class="btn btn-primary" disabled>
                                <i class="fas fa-magic"></i> <span data-translate="generateQuestions">Generate Questions</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Screen -->
            <div id="processingScreen" class="screen">
                <div class="ai-processing-container">
                    <!-- Animated Background -->
                    <div class="processing-bg">
                        <div class="neural-network">
                            <div class="node node-1"></div>
                            <div class="node node-2"></div>
                            <div class="node node-3"></div>
                            <div class="node node-4"></div>
                            <div class="node node-5"></div>
                            <div class="connection conn-1"></div>
                            <div class="connection conn-2"></div>
                            <div class="connection conn-3"></div>
                            <div class="connection conn-4"></div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="processing-content">
                        <!-- AI Brain Animation -->
                        <div class="ai-brain-container">
                            <div class="brain-core">
                                <div class="brain-pulse"></div>
                                <div class="brain-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="thinking-dots">
                                    <span class="dot dot-1"></span>
                                    <span class="dot dot-2"></span>
                                    <span class="dot dot-3"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Title and Status -->
                        <div class="processing-info">
                            <h1 class="processing-title">
                                <span class="title-gradient" data-translate="aiQuestionGenerator">AI Question Generator</span>
                            </h1>
                            <p id="processingStatus" class="processing-status" data-translate="initializingAiModels">Initializing AI models...</p>
                        </div>

                        <!-- Advanced Progress Bar -->
                        <div class="progress-container">
                            <div class="progress-track">
                                <div id="progressFill" class="progress-fill">
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-particles">
                                    <div class="particle particle-1"></div>
                                    <div class="particle particle-2"></div>
                                    <div class="particle particle-3"></div>
                                </div>
                            </div>
                            <div class="progress-info">
                                <span id="progressText" class="progress-percentage">0%</span>
                                <span id="progressETA" class="progress-eta" data-translate="calculating">Calculating...</span>
                            </div>
                        </div>

                        <!-- Processing Steps -->
                        <div class="processing-steps">
                            <div class="step step-1" id="step1">
                                <div class="step-icon"><i class="fas fa-file-text"></i></div>
                                <span class="step-text" data-translate="analyzingContent">Analyzing Content</span>
                            </div>
                            <div class="step step-2" id="step2">
                                <div class="step-icon"><i class="fas fa-cogs"></i></div>
                                <span class="step-text" data-translate="processingAiModels">Processing AI Models</span>
                            </div>
                            <div class="step step-3" id="step3">
                                <div class="step-icon"><i class="fas fa-lightbulb"></i></div>
                                <span class="step-text" data-translate="generatingQuestions">Generating Questions</span>
                            </div>
                            <div class="step step-4" id="step4">
                                <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                                <span class="step-text" data-translate="finalizing">Finalizing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Display Screen -->
            <div id="questionsScreen" class="screen">
                <div class="questions-container">
                    <div class="screen-header">
                        <div class="header-nav">
                            <button id="backToContent" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                            </button>
                        </div>
                        <h2 data-translate="generatedQuestions">Generated Questions</h2>
                        <div class="question-actions">
                            <button id="startQuizBtn" class="btn btn-primary">
                                <i class="fas fa-play"></i> <span data-translate="startQuiz">Start Quiz</span>
                            </button>
                            <button id="showAnswersBtn" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> <span data-translate="showQuestionsWithAnswers">Show Questions with Answers</span>
                            </button>
                            <button id="exportQuestionsBtn" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-download"></i> <span data-translate="exportPdf">Export PDF</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="questionsDisplay" class="questions-display">
                        <!-- Questions will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Quiz Screen -->
            <div id="quizScreen" class="screen">
                <div class="quiz-container">
                    <div class="quiz-header">
                        <div class="quiz-nav">
                            <!-- Main menu button removed - using floating home button instead -->
                        </div>
                        <div class="quiz-progress">
                            <span id="questionNumber"><span data-translate="question">Question</span> 1</span>
                            <span id="questionCount"><span data-translate="of">of</span> 15</span>
                        </div>
                        <div class="quiz-score">
                            <span data-translate="score">Score: </span>
                            <span id="currentScore">0/0</span>
                        </div>
                    </div>
                    
                    <div class="quiz-content">
                        <div id="quizQuestion" class="quiz-question">
                            <!-- Current question will be displayed here -->
                        </div>
                        
                        <div id="quizOptions" class="quiz-options">
                            <!-- Answer options will be displayed here -->
                        </div>
                        
                        <div id="quizFeedback" class="quiz-feedback hidden">
                            <!-- Feedback will be displayed here -->
                        </div>
                        
                        <div class="quiz-actions">
                            <button id="submitAnswer" class="btn btn-primary" disabled>
                                <span data-translate="submitAnswer">Submit Answer</span>
                            </button>
                            <button id="nextQuestion" class="btn btn-secondary hidden">
                                <span data-translate="nextQuestion">Next Question</span>
                            </button>
                            <button id="finishQuiz" class="btn btn-success hidden">
                                <span data-translate="finishQuiz">Finish Quiz</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="resultsScreen" class="screen">
                <div class="results-container">
                    <div class="results-header">
                        <i class="fas fa-trophy results-icon"></i>
                        <h2 data-translate="quizResults">Quiz Results</h2>
                    </div>
                    
                    <div id="resultsDisplay" class="results-display">
                        <!-- Results will be dynamically inserted here -->
                    </div>
                    
                    <div class="results-actions">
                        <button id="newQuizBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> <span data-translate="newQuiz">New Quiz</span>
                        </button>
                        <button id="reviewAnswersBtn" class="btn btn-secondary">
                            <i class="fas fa-eye"></i> <span data-translate="reviewAnswers">Review Answers</span>
                        </button>
                        <button id="saveQuizBtn" class="btn btn-success">
                            <i class="fas fa-bookmark"></i> <span data-translate="saveQuiz">Save Quiz</span>
                        </button>
                    </div>

                    <!-- Inline Save Quiz Form -->
                    <div id="inlineSaveQuizForm" class="inline-save-form" style="display: none;">
                        <div class="save-form-container">
                            <div class="save-form-header">
                                <div class="save-form-icon">
                                    <i class="fas fa-bookmark"></i>
                                </div>
                                <div class="save-form-title">
                                    <h3 data-translate="saveQuizTitle">Save Quiz</h3>
                                    <p data-translate="saveQuizSubtitle">Give your quiz a memorable name</p>
                                </div>
                            </div>

                            <div class="save-form-content">
                                <div class="quiz-preview-card">
                                    <div class="preview-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-list"></i>
                                            <span class="stat-label" data-translate="type">Type:</span>
                                            <span class="stat-value" id="inlinePreviewType">MCQ</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-question-circle"></i>
                                            <span class="stat-label" data-translate="questions">Questions:</span>
                                            <span class="stat-value" id="inlinePreviewCount">0</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-file"></i>
                                            <span class="stat-label" data-translate="source">Source:</span>
                                            <span class="stat-value" id="inlinePreviewSource">Text Content</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="name-input-group">
                                    <label for="inlineQuizName" data-translate="quizNameLabel">Quiz Name:</label>
                                    <div class="input-wrapper">
                                        <input type="text" id="inlineQuizName" class="quiz-name-input" placeholder="Enter quiz name..." maxlength="100">
                                        <div class="input-decoration"></div>
                                    </div>
                                    <small class="input-hint" data-translate="quizNameHint">Leave empty to use default name</small>
                                </div>

                                <div class="save-form-actions">
                                    <button id="confirmInlineSave" class="btn btn-primary save-btn">
                                        <i class="fas fa-save"></i>
                                        <span data-translate="saveQuiz">Save Quiz</span>
                                    </button>
                                    <button id="cancelInlineSave" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times"></i>
                                        <span data-translate="cancel">Cancel</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>

        <!-- API Key Management Dialog -->
        <div id="apiKeyDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-key"></i> <span data-translate="apiKeyManager">API Key Manager</span></h3>
                    <button id="closeApiKeyDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="dialog-body">
                    <div id="apiKeyAlertContainer"></div>

                    <!-- Current API Key Status -->
                    <div class="api-key-section">
                        <h4 data-translate="currentApiKey">Current API Key Status</h4>
                        <div id="currentApiKeyInfo" class="api-key-info">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span data-translate="loading">Loading API key information...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Update API Key -->
                    <div class="api-key-section">
                        <h4 data-translate="newApiKey">Update API Key</h4>
                        <div class="input-group">
                            <label for="newApiKeyInput" data-translate="enterNewApiKey">New OpenRouter API Key:</label>
                            <div class="input-with-toggle">
                                <input type="password" id="newApiKeyInput" placeholder="sk-or-v1-..." maxlength="73" class="api-key-input">
                                <button type="button" id="toggleApiKeyVisibility" class="toggle-btn">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="help-text">
                                Enter your new OpenRouter API key. It should start with "sk-or-v1-" and be 73 characters long.
                            </div>
                        </div>
                        <div class="button-group">
                            <button id="updateApiKeyBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> <span data-translate="updateApiKey">Update API Key</span>
                            </button>
                            <button id="testApiKeyBtn" class="btn btn-secondary">
                                <i class="fas fa-flask"></i> Test Current Key
                            </button>
                        </div>
                    </div>

                    <!-- Test Results -->
                    <div id="apiKeyTestResults" class="api-key-section" style="display: none;">
                        <h4 data-translate="testResults">Test Results</h4>
                        <div id="testResultsContent"></div>
                    </div>

                    <!-- Instructions -->
                    <div class="api-key-section">
                        <h4><i class="fas fa-info-circle"></i> <span data-translate="quickInstructions">Quick Instructions</span></h4>
                        <ol class="instructions-list">
                            <li>Visit <a href="https://openrouter.ai/" target="_blank">OpenRouter.ai</a> and create an account</li>
                            <li>Go to Settings → API Keys and create a new key</li>
                            <li>Go to Settings → Privacy and enable "Prompt Training"</li>
                            <li>Paste your new key above and click "Update API Key"</li>
                            <li>Use "Test Current Key" to verify it works</li>
                        </ol>
                    </div>
                </div>

                <div class="dialog-footer">
                    <button id="cancelApiKeyDialog" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="close">Close</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- History Screen -->
        <div id="historyScreen" class="screen">
            <div class="content-container">
                <div class="screen-header">
                    <div class="header-left">
                        <button id="backToMainFromHistory" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                    </div>
                    <h2><i class="fas fa-history"></i> <span data-translate="quizHistory">Quiz History</span></h2>
                    <div class="header-right">
                        <div id="historyRefreshIndicator" class="refresh-indicator" style="display: none;">
                            <i class="fas fa-check-circle text-success"></i> <span>Updated</span>
                        </div>
                        <button id="refreshHistoryBtn" class="btn btn-sm btn-primary" title="Refresh Now">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="clearHistoryBtn" class="btn btn-danger">
                            <i class="fas fa-trash"></i> <span data-translate="clearHistory">Clear History</span>
                        </button>
                    </div>
                </div>

                <div class="history-content">


                    <div class="history-stats-summary">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalQuizzesHistory">0</div>
                                <div class="stat-label" data-translate="totalQuizzes">Total Quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgScoreHistory">0%</div>
                                <div class="stat-label" data-translate="averageScore">Average Score</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgTimeHistory">0m</div>
                                <div class="stat-label" data-translate="averageTime">Average Time</div>
                            </div>
                        </div>
                    </div>

                    <!-- Saved Quizzes Section -->
                    <div class="saved-quizzes-section">
                        <div class="section-header">
                            <h3><i class="fas fa-bookmark"></i> <span data-translate="savedQuizzes">Saved Quizzes</span></h3>
                            <div class="section-actions">
                                <button id="refreshSavedQuizzesBtn" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-sync-alt"></i> <span data-translate="refresh">Refresh</span>
                                </button>
                                <button id="clearAllSavedQuizzesBtn" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash-alt"></i> <span data-translate="clearAllSaved">Clear All</span>
                                </button>
                            </div>
                        </div>
                        <div class="saved-quizzes-list" id="savedQuizzesList">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p data-translate="loadingSavedQuizzes">Loading saved quizzes...</p>
                            </div>
                        </div>

                        <!-- Saved Quizzes Pagination -->
                        <div class="pagination-container" id="savedQuizzesPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="savedQuizzesPageInfo">Page 1 of 1</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="savedQuizzesPrevBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span id="savedQuizzesPageNumbers" class="page-numbers"></span>
                                <button id="savedQuizzesNextBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz History Section -->
                    <div class="quiz-history-section">
                        <div class="section-header">
                            <h3><i class="fas fa-history"></i> <span data-translate="completedQuizzes">Completed Quizzes</span></h3>
                            <div class="history-filters-compact">
                                <select id="historyDateFilter" class="filter-select-compact">
                                    <option value="all" data-translate="allTime">All Time</option>
                                    <option value="today" data-translate="today">Today</option>
                                    <option value="yesterday" data-translate="yesterday">Yesterday</option>
                                    <option value="week" data-translate="thisWeek">This Week</option>
                                    <option value="month" data-translate="thisMonth">This Month</option>
                                </select>
                                <select id="historyTypeFilter" class="filter-select-compact">
                                    <option value="all" data-translate="allTypes">All Types</option>
                                    <option value="MCQ" data-translate="multipleChoice">Multiple Choice</option>
                                    <option value="TF" data-translate="trueFalse">True/False</option>
                                </select>
                            </div>
                        </div>
                        <div class="history-list" id="historyList">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p data-translate="loadingQuizHistory">Loading quiz history...</p>
                            </div>
                        </div>

                        <!-- History Pagination -->
                        <div class="pagination-container" id="historyPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="historyPageInfo">Page 1 of 1</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="historyPrevBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span id="historyPageNumbers" class="page-numbers"></span>
                                <button id="historyNextBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Screen -->
        <div id="statisticsScreen" class="screen">
            <div class="content-container">
                <div class="screen-header">
                    <div class="header-left">
                        <button id="backToMainFromStats" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                    </div>
                    <h2><i class="fas fa-chart-bar"></i> <span data-translate="statistics">Statistics</span></h2>
                    <div class="header-right">
                        <div id="statsRefreshIndicator" class="refresh-indicator" style="display: none;">
                            <i class="fas fa-check-circle text-success"></i> <span>Updated</span>
                        </div>
                        <button id="refreshStatsBtn" class="btn btn-sm btn-primary" title="Refresh Now">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="exportStatsBtn" class="btn btn-primary">
                            <i class="fas fa-download"></i> <span data-translate="exportStats">Export Stats</span>
                        </button>
                    </div>
                </div>

                <div class="statistics-content">
                    <div class="stats-overview">
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="overallScore">0%</div>
                                <div class="stat-label" data-translate="overallScore">Overall Score</div>
                                <div class="stat-sublabel" data-translate="acrossAllQuizzes">Across all quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="currentStreak">0</div>
                                <div class="stat-label" data-translate="currentStreak">Current Streak</div>
                                <div class="stat-sublabel" data-translate="daysInARow">Days in a row</div>
                            </div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stats-section">
                            <h3><i class="fas fa-chart-line"></i> <span data-translate="performanceMetrics">Performance Metrics</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="totalQuestions">0</div>
                                        <div class="stat-label" data-translate="totalQuestions">Total Questions</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="correctAnswers">0</div>
                                        <div class="stat-label" data-translate="correctAnswers">Correct Answers</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="incorrectAnswers">0</div>
                                        <div class="stat-label" data-translate="incorrectAnswers">Incorrect Answers</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-chart-pie"></i> <span data-translate="questionTypes">Question Types</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="mcqQuizzes">0</div>
                                        <div class="stat-label" data-translate="mcqQuizzes">MCQ Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="mcqProgress"></div>
                                            </div>
                                            <span class="progress-text" id="mcqPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="tfQuizzes">0</div>
                                        <div class="stat-label" data-translate="trueFalseQuizzes">True/False Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="tfProgress"></div>
                                            </div>
                                            <span class="progress-text" id="tfPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-calendar-alt"></i> <span data-translate="activity">Activity</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesToday">0</div>
                                        <div class="stat-label" data-translate="quizzesToday">Quizzes Today</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisWeek">0</div>
                                        <div class="stat-label" data-translate="thisWeek">This Week</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisMonth">0</div>
                                        <div class="stat-label" data-translate="thisMonth">This Month</div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>


                </div>
            </div>
        </div>

        <!-- Floating Back Button -->
        <button id="floatingBackBtn" class="floating-back-btn" title="Back to Main Menu" style="display: none;">
            <i class="fas fa-home"></i>
        </button>

        <!-- Notification Container -->
        <div id="notificationContainer" class="notification-container"></div>

        <!-- Model Management Dialogs -->

        <!-- Add Model Dialog -->
        <div id="addModelDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-plus"></i> <span data-translate="addNewAiModel">Add New AI Model</span></h3>
                    <button id="closeAddModelDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="newModelId" data-translate="modelId">Model ID:</label>
                        <input type="text" id="newModelId" class="form-input" placeholder="e.g., openai/gpt-4:free" required>
                        <small class="form-help" data-translate="modelIdHelp">Enter the full model identifier (provider/model-name:tier)</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelName" data-translate="displayName">Display Name:</label>
                        <input type="text" id="newModelName" class="form-input" placeholder="e.g., GPT-4 (Free)" required>
                        <small class="form-help" data-translate="displayNameHelp">Friendly name to display in the dropdown</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelDescription" data-translate="descriptionOptional">Description (Optional):</label>
                        <input type="text" id="newModelDescription" class="form-input" placeholder="e.g., Advanced reasoning model">
                        <small class="form-help" data-translate="descriptionHelp">Brief description of the model's capabilities</small>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="confirmAddModel" class="btn btn-primary">
                        <i class="fas fa-plus"></i> <span data-translate="addModel">Add Model</span>
                    </button>
                    <button id="cancelAddModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="cancel">Cancel</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Remove Model Dialog -->
        <div id="removeModelDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-trash"></i> <span data-translate="removeAiModel">Remove AI Model</span></h3>
                    <button id="closeRemoveModelDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="removeModelSelect" data-translate="selectModelToRemove">Select Model to Remove:</label>
                        <select id="removeModelSelect" class="form-select" required>
                            <option value="" data-translate="chooseModelToRemove">Choose a model to remove...</option>
                        </select>
                        <small class="form-help" data-translate="removeModelWarning">⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.</small>
                    </div>
                    <div id="removeModelInfo" class="model-info" style="display: none;">
                        <div class="info-item">
                            <strong data-translate="modelId">Model ID:</strong> <span id="removeModelId"></span>
                        </div>
                        <div class="info-item">
                            <strong data-translate="displayName">Display Name:</strong> <span id="removeModelName"></span>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="confirmRemoveModel" class="btn btn-danger" disabled>
                        <i class="fas fa-trash"></i> <span data-translate="removeModel">Remove Model</span>
                    </button>
                    <button id="cancelRemoveModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="cancel">Cancel</span>
                    </button>
                </div>
            </div>
        </div>



        <!-- Test Models Dialog -->
        <div id="testModelsDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-flask"></i> <span data-translate="modelTestingSimulation">Model Testing & Simulation</span></h3>
                    <button id="closeTestModelsDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="test-controls">
                        <div class="form-group">
                            <label for="testContent" data-translate="testContent">Test Content:</label>
                            <textarea id="testContent" class="form-input" rows="4" data-translate="testContentPlaceholder" placeholder="Enter test content for question generation...">The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.</textarea>
                        </div>
                        <div class="form-group">
                            <label for="testQuestionType" data-translate="questionType">Question Type:</label>
                            <select id="testQuestionType" class="form-select">
                                <option value="MCQ" data-translate="multipleChoiceMcq">Multiple Choice (MCQ)</option>
                                <option value="TF" data-translate="trueFalseTf">True/False (TF)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="testQuestionCount" data-translate="questionCount">Question Count:</label>
                            <input type="number" id="testQuestionCount" class="form-input" value="3" min="1" max="10">
                        </div>
                        <div class="test-actions">
                            <button id="startModelTest" class="btn btn-primary">
                                <i class="fas fa-play"></i> <span data-translate="startTestingAllModels">Start Testing All Models</span>
                            </button>
                            <button id="stopModelTest" class="btn btn-danger" style="display: none;">
                                <i class="fas fa-stop"></i> <span data-translate="stopTesting">Stop Testing</span>
                            </button>
                            <button id="clearRateLimits" class="btn btn-warning">
                                <i class="fas fa-refresh"></i> <span data-translate="clearRateLimits">Clear Rate Limits</span>
                            </button>
                        </div>
                    </div>
                    <div class="test-results" id="testResults">
                        <div class="test-placeholder">
                            <i class="fas fa-flask"></i>
                            <p data-translate="clickStartTesting">Click "Start Testing" to test all available models</p>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="exportTestResults" class="btn btn-primary" style="display: none;">
                        <i class="fas fa-download"></i> <span data-translate="exportResults">Export Results</span>
                    </button>
                    <button id="closeTestModels" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="close">Close</span>
                    </button>
                </div>
            </div>
        </div>
    </div>



    <script src="translations.js"></script>
    <script src="app.js"></script>
</body>
</html>
