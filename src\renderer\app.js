// Main application logic for the renderer process
class QuestionGeneratorApp {
    constructor() {
        this.currentScreen = 'homeScreen';
        this.selectedQuestionType = null;
        this.selectedInputMethod = null;
        this.currentQuestions = [];
        this.currentQuiz = null;
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: 0 },
            startTime: null,
            endTime: null
        };

        // Initialize model preference (will be loaded from settings)
        this.savedModelPreference = null;

        // Pagination properties
        this.savedQuizzesPage = 1;
        this.savedQuizzesPerPage = 6;
        this.allSavedQuizzes = [];
        this.historyPage = 1;
        this.historyPerPage = 6;
        this.allHistorySessions = [];

        // Image to PDF state
        this.selectedImages = [];
        this.pdfSettings = {
            quality: 'medium',
            pageSize: 'a4',
            orientation: 'auto',
            margin: 'small'
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMenuListeners();
        this.setupDragAndDrop();
        this.initializeLanguage();
        this.showScreen('homeScreen');
    }

    initializeLanguage() {
        // Initialize the language system
        if (window.initializeLanguage) {
            window.initializeLanguage();
        }
    }

    setupEventListeners() {
        // Home screen service launchers
        document.getElementById('quizGeneratorService').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        // Quiz Generator Screen - Question type selection
        document.getElementById('mcqBtn').addEventListener('click', () => {
            this.selectQuestionType('MCQ');
        });

        document.getElementById('tfBtn').addEventListener('click', () => {
            this.selectQuestionType('TF');
        });

        // Input method selection
        document.getElementById('textInputBtn').addEventListener('click', () => {
            this.selectInputMethod('text');
        });

        document.getElementById('fileUploadBtn').addEventListener('click', () => {
            this.selectInputMethod('file');
        });

        document.getElementById('imageUploadBtn').addEventListener('click', () => {
            this.selectInputMethod('image');
        });

        // Navigation
        document.getElementById('backToQuizGenerator').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('backToHome').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Home language toggle
        document.getElementById('homeLanguageToggle').addEventListener('click', () => {
            if (window.toggleLanguage) {
                window.toggleLanguage();
                this.updateHomeLanguageToggle();
            }
        });

        // Image to PDF functionality
        this.setupImageToPdfListeners();

        // 3D Anatomy Explorer functionality
        this.setupAnatomyExplorerListeners();

        document.getElementById('backToContent').addEventListener('click', () => {
            this.showScreen('contentScreen');
        });

        // Content generation
        document.getElementById('generateFromText').addEventListener('click', () => {
            this.generateFromText();
        });

        document.getElementById('generateFromFile').addEventListener('click', () => {
            this.generateFromFile();
        });

        document.getElementById('generateFromImage').addEventListener('click', () => {
            this.generateFromImage();
        });

        // File upload - for desktop, we'll use the file dialog instead
        document.getElementById('fileInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                // For desktop, trigger file selection dialog instead
                await this.generateFromFile();
            }
        });

        document.getElementById('imageInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                // For desktop, trigger file selection dialog instead
                await this.generateFromImage();
            }
        });

        // File removal
        document.getElementById('removeFile').addEventListener('click', () => {
            this.removeFile();
        });

        document.getElementById('removeImage').addEventListener('click', () => {
            this.removeImage();
        });

        // Quiz actions
        document.getElementById('startQuizBtn').addEventListener('click', () => {
            this.startInteractiveQuiz();
        });

        document.getElementById('showAnswersBtn').addEventListener('click', () => {
            this.showQuestionsWithAnswers();
        });

        document.getElementById('exportQuestionsBtn').addEventListener('click', () => {
            this.exportQuestionsToPDF();
        });

        document.getElementById('submitAnswer').addEventListener('click', (e) => {
            console.log('Submit button clicked');
            e.preventDefault(); // Prevent any default behavior
            this.submitQuizAnswer();
        });

        document.getElementById('nextQuestion').addEventListener('click', () => {
            this.nextQuizQuestion();
        });

        document.getElementById('finishQuiz').addEventListener('click', () => {
            this.finishQuiz();
        });

        // Results actions
        document.getElementById('newQuizBtn').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('reviewAnswersBtn').addEventListener('click', () => {
            this.reviewAnswers();
        });

        document.getElementById('saveQuizBtn').addEventListener('click', () => {
            this.saveQuizForLater();
        });





        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });

        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStatistics();
        });

        // Back to main menu buttons - removed, using floating home button instead

        // History and Statistics buttons
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistoryScreen();
        });

        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStatisticsScreen();
        });

        // Header back button
        document.getElementById('headerBackBtn').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Floating back button
        document.getElementById('floatingBackBtn').addEventListener('click', () => {
            // Check if we're in a quiz and need confirmation
            const currentScreen = document.querySelector('.screen.active')?.id;
            if (currentScreen === 'quizScreen') {
                this.confirmBackToMain('Are you sure you want to exit the quiz? Your progress will be lost.');
            } else {
                this.showScreen('homeScreen');
            }
        });

        // History screen navigation
        document.getElementById('backToMainFromHistory').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Manual refresh buttons
        document.getElementById('refreshHistoryBtn').addEventListener('click', async () => {
            await this.refreshHistoryData();
        });

        document.getElementById('refreshStatsBtn').addEventListener('click', async () => {
            await this.refreshStatisticsData();
        });

        // Main menu buttons removed - using floating home button instead

        // Statistics screen navigation
        document.getElementById('backToMainFromStats').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // History actions
        document.getElementById('clearHistoryBtn').addEventListener('click', () => {
            this.clearHistory();
        });

        document.getElementById('refreshHistoryBtn').addEventListener('click', () => {
            this.loadHistory();
        });

        document.getElementById('refreshSavedQuizzesBtn').addEventListener('click', () => {
            this.loadSavedQuizzes();
        });

        document.getElementById('clearAllSavedQuizzesBtn').addEventListener('click', () => {
            this.clearAllSavedQuizzes();
        });

        // Pagination event listeners
        document.getElementById('savedQuizzesPrevBtn').addEventListener('click', () => {
            this.changeSavedQuizzesPage(-1);
        });

        document.getElementById('savedQuizzesNextBtn').addEventListener('click', () => {
            this.changeSavedQuizzesPage(1);
        });

        document.getElementById('historyPrevBtn').addEventListener('click', () => {
            this.changeHistoryPage(-1);
        });

        document.getElementById('historyNextBtn').addEventListener('click', () => {
            this.changeHistoryPage(1);
        });

        // History filters
        document.getElementById('historyTypeFilter').addEventListener('change', () => {
            this.filterHistory();
        });

        document.getElementById('historyDateFilter').addEventListener('change', () => {
            this.filterHistory();
        });

        // Debug: Add test save functionality (remove this later)
        if (document.getElementById('testSaveBtn')) {
            document.getElementById('testSaveBtn').addEventListener('click', async () => {
                console.log('Testing save functionality...');
                const testQuiz = {
                    id: 'test-' + Date.now(),
                    title: 'Test Quiz',
                    questionType: 'MCQ',
                    questions: [
                        { question: 'Test question?', answer: 'A', options: ['A', 'B', 'C', 'D'] }
                    ],
                    createdAt: new Date().toISOString(),
                    source: 'Test Source'
                };

                const result = await window.electronAPI.saveSavedQuiz(testQuiz);
                console.log('Test save result:', result);

                if (result.success) {
                    this.showNotification('Test quiz saved!', 'success');
                    await this.loadSavedQuizzes();
                } else {
                    this.showNotification('Test save failed: ' + result.error, 'error');
                }
            });
        }

        // Statistics actions
        document.getElementById('exportStatsBtn').addEventListener('click', () => {
            this.exportStatistics();
        });



        // Question count settings
        this.setupQuestionCountListeners();

        // Load settings immediately when event listeners are set up
        setTimeout(() => {
            this.loadQuestionCountSettings();
            this.initializeModelSelection();
            this.initializeModelManagement();
        }, 100);
    }

    setupMenuListeners() {
        // Listen for menu events from main process
        window.electronAPI.onNewQuiz(() => {
            this.showScreen('quizGeneratorScreen');
        });

        window.electronAPI.onGenerateMCQ(() => {
            this.selectQuestionType('MCQ');
            this.selectInputMethod('text');
        });

        window.electronAPI.onGenerateTF(() => {
            this.selectQuestionType('TF');
            this.selectInputMethod('text');
        });

        window.electronAPI.onStartQuiz(() => {
            if (this.currentQuestions.length > 0) {
                this.startInteractiveQuiz();
            } else {
                this.showNotification('No questions available. Please generate questions first.', 'warning');
            }
        });



        window.electronAPI.onStatistics(() => {
            this.showStatistics();
        });

        window.electronAPI.onHistory(() => {
            this.showHistory();
        });

        window.electronAPI.onAbout(() => {
            this.showAbout();
        });

        window.electronAPI.onHelp(() => {
            this.showHelp();
        });

        window.electronAPI.onFileSelected((event, filePath) => {
            this.handleExternalFile(filePath);
        });
    }

    setupDragAndDrop() {
        // File drop zone
        const fileDropZone = document.getElementById('fileDropZone');
        const imageDropZone = document.getElementById('imageDropZone');

        [fileDropZone, imageDropZone].forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('dragover');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');
            });

            zone.addEventListener('drop', async (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    // In Electron, we can access the file path directly from dropped files
                    if (file.path) {
                        if (zone === fileDropZone) {
                            await this.processFile({ path: file.path });
                        } else {
                            // Check if it's an image
                            const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                            const isImage = imageExtensions.some(ext =>
                                file.path.toLowerCase().endsWith(ext)
                            );

                            if (isImage) {
                                await this.processFile({ path: file.path });
                            } else {
                                this.showNotification('Please drop an image file', 'warning');
                            }
                        }
                    } else {
                        this.showNotification('Unable to access file path', 'error');
                    }
                }
            });

            zone.addEventListener('click', async () => {
                if (zone === fileDropZone) {
                    await this.generateFromFile();
                } else {
                    await this.generateFromImage();
                }
            });
        });
    }

    setupQuestionCountListeners() {
        // File questions count input
        const fileQuestionsInput = document.getElementById('fileQuestionsCount');
        const imageQuestionsInput = document.getElementById('imageQuestionsCount');

        // Load saved values on startup - try multiple times to ensure it works
        this.loadQuestionCountSettings();
        setTimeout(() => {
            this.loadQuestionCountSettings();
        }, 500);
        setTimeout(() => {
            this.loadQuestionCountSettings();
        }, 1000);

        // Save when values change
        fileQuestionsInput.addEventListener('change', () => {
            this.saveQuestionCountSetting('questionsPerPage', parseInt(fileQuestionsInput.value));
        });

        imageQuestionsInput.addEventListener('change', () => {
            this.saveQuestionCountSetting('imageQuestionsCount', parseInt(imageQuestionsInput.value));
        });

        // Validate input ranges
        fileQuestionsInput.addEventListener('input', () => {
            const value = parseInt(fileQuestionsInput.value);
            if (value < 1) fileQuestionsInput.value = 1;
            if (value > 10) fileQuestionsInput.value = 10;
        });

        imageQuestionsInput.addEventListener('input', () => {
            const value = parseInt(imageQuestionsInput.value);
            if (value < 1) imageQuestionsInput.value = 1;
            if (value > 15) imageQuestionsInput.value = 15;
        });
    }

    async loadQuestionCountSettings() {
        try {
            if (!window.electronAPI || !window.electronAPI.getSettings) {
                console.warn('electronAPI not ready yet, will retry...');
                return;
            }

            const result = await window.electronAPI.getSettings();
            if (result.success && result.settings) {
                const fileInput = document.getElementById('fileQuestionsCount');
                const imageInput = document.getElementById('imageQuestionsCount');

                if (fileInput) {
                    fileInput.value = result.settings.questionsPerPage || 5;
                    console.log(`Loaded file questions per page: ${fileInput.value}`);
                }
                if (imageInput) {
                    imageInput.value = result.settings.imageQuestionsCount || 5;
                    console.log(`Loaded image questions count: ${imageInput.value}`);
                }

                // Update app settings
                this.appSettings = result.settings;
                console.log('Settings loaded successfully:', result.settings);
            } else {
                console.warn('Failed to load settings:', result);
            }
        } catch (error) {
            console.warn('Could not load question count settings:', error);
        }
    }

    async saveQuestionCountSetting(key, value) {
        try {
            const settings = { [key]: value };
            const result = await window.electronAPI.saveSettings(settings);
            if (result.success) {
                // Update app settings
                if (!this.appSettings) this.appSettings = {};
                this.appSettings[key] = value;

                this.showNotification(`${key === 'questionsPerPage' ? 'Questions per page' : 'Image questions'} updated to ${value}`, 'success');
            } else {
                this.showNotification('Failed to save setting', 'error');
            }
        } catch (error) {
            console.error('Error saving question count setting:', error);
            this.showNotification('Error saving setting', 'error');
        }
    }

    selectQuestionType(type) {
        this.selectedQuestionType = type;
        
        // Update UI
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        if (type === 'MCQ') {
            document.getElementById('mcqBtn').classList.add('selected');
        } else {
            document.getElementById('tfBtn').classList.add('selected');
        }

        this.showNotification(`Selected ${type} questions`, 'success');
    }

    selectInputMethod(method) {
        this.selectedInputMethod = method;
        
        if (!this.selectedQuestionType) {
            this.showNotification('Please select a question type first', 'warning');
            return;
        }

        this.showScreen('contentScreen');
        
        // Update screen title
        const title = document.getElementById('contentScreenTitle');
        if (window.t) {
            // Use translations if available
            const questionType = this.selectedQuestionType === 'MCQ' ? window.t('multipleChoice') : window.t('trueFalse');
            title.textContent = `${window.t('addContent')} ${questionType}`;
        } else {
            title.textContent = `Add Content for ${this.selectedQuestionType} Questions`;
        }

        // Show appropriate input area
        document.querySelectorAll('.input-area').forEach(area => {
            area.classList.remove('active');
        });

        switch (method) {
            case 'text':
                document.getElementById('textInputArea').classList.add('active');
                document.getElementById('textContent').focus();
                break;
            case 'file':
                document.getElementById('fileUploadArea').classList.add('active');
                break;
            case 'image':
                document.getElementById('imageUploadArea').classList.add('active');
                break;
        }
    }

    showScreen(screenId) {
        console.log(`Attempting to show screen: ${screenId}`);

        // Cleanup anatomy viewer if leaving anatomy screen
        const currentScreen = document.querySelector('.screen.active');
        if (currentScreen && currentScreen.id === 'anatomyExplorerScreen' && screenId !== 'anatomyExplorerScreen') {
            this.cleanupAnatomyViewer();
        }

        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show target screen
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            console.log(`Found screen element: ${screenId}`);
            targetScreen.classList.add('active');
            this.currentScreen = screenId;

            // Update header visibility
            this.updateHeaderVisibility(screenId);

            // Manage header back button visibility
            this.updateHeaderBackButton(screenId);

            // Update dynamic translations for the new screen
            if (window.updateDynamicContent) {
                setTimeout(() => window.updateDynamicContent(), 100);
            }
        } else {
            console.error(`Screen element not found: ${screenId}`);
        }
    }

    updateHeaderVisibility(screenId) {
        const header = document.querySelector('.app-header');

        // Show header only on MCQ/TF quiz generator screens
        const quizGeneratorScreens = [
            'quizGeneratorScreen',
            'contentScreen',
            'questionsScreen'
        ];

        if (quizGeneratorScreens.includes(screenId)) {
            header.style.display = 'block';
            document.body.classList.remove('home-active');
        } else {
            header.style.display = 'none';
            document.body.classList.add('home-active');
        }
    }

    updateHeaderBackButton(screenId) {
        const headerBackBtn = document.getElementById('headerBackBtn');
        const floatingBackBtn = document.getElementById('floatingBackBtn');

        // Show floating home button on ALL pages except home screen
        if (screenId === 'homeScreen') {
            headerBackBtn.style.display = 'none';
            floatingBackBtn.style.display = 'none';
        } else {
            headerBackBtn.style.display = 'none'; // Hide header back button (we only want floating)
            floatingBackBtn.style.display = 'flex'; // Show floating home button on all other screens
        }
    }

    updateHomeLanguageToggle() {
        const homeLanguageToggle = document.getElementById('homeLanguageToggle');
        if (homeLanguageToggle) {
            const languageText = homeLanguageToggle.querySelector('.language-text');
            if (languageText) {
                const currentLanguage = localStorage.getItem('language') || 'en';
                languageText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
            }
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    async generateFromText() {
        const textContent = document.getElementById('textContent').value.trim();
        
        if (!textContent) {
            this.showNotification('Please enter some text content', 'warning');
            return;
        }

        if (textContent.length < 50) {
            this.showNotification('Please enter more content (at least 50 characters)', 'warning');
            return;
        }

        await this.generateQuestions(textContent);
    }

    async generateFromFile() {
        try {
            // Use file selection dialog for desktop app
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                await this.processFile({ path: fileSelection.filePath });
            } else {
                this.showNotification('No file selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting file:', error);
            this.showNotification('Error selecting file', 'error');
        }
    }

    async generateFromImage() {
        try {
            // Use file selection dialog for desktop app (images)
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                // Check if it's an image file
                const filePath = fileSelection.filePath;
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                if (isImage) {
                    await this.processFile({ path: filePath });
                } else {
                    this.showNotification('Please select an image file', 'warning');
                }
            } else {
                this.showNotification('No image selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting image:', error);
            this.showNotification('Error selecting image', 'error');
        }
    }

    async processFile(file) {
        try {
            this.showProcessingScreen();
            this.updateProgress(10, 'Analyzing file structure...', '~30 seconds');

            // For desktop app, we need to use the file selection dialog
            // since we can't access file.path directly from the file input
            let filePath;

            if (file && file.path) {
                // If file has path (from drag & drop or external selection)
                filePath = file.path;
            } else {
                // Use file selection dialog
                const fileSelection = await window.electronAPI.selectFile();
                if (fileSelection.success) {
                    filePath = fileSelection.filePath;
                } else {
                    throw new Error('No file selected');
                }
            }

            // Process file through main process
            const result = await window.electronAPI.processFile(filePath);

            if (result.success) {
                this.updateProgress(50, 'Extracting content with AI...', '~20 seconds');

                // Detect if this is an image file
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                // Store page count for per-page calculation
                const pageCount = result.pageCount || 1;
                console.log(`Document has ${pageCount} pages`);

                await this.generateQuestions(result.text, isImage, pageCount);
            } else {
                throw new Error(result.error || 'Failed to process file');
            }
        } catch (error) {
            console.error('File processing error:', error);
            this.showNotification(`Error processing file: ${error.message}`, 'error');
            this.showScreen('contentScreen');
        }
    }

    async generateQuestions(content, isImage = false, pageCount = 1) {
        try {
            // Store parameters for retry functionality
            this.lastGenerationParams = { content, isImage, pageCount };

            this.showProcessingScreen();

            // Get question count from the UI inputs or app settings FIRST
            let questionsPerPage = 5; // Default fallback
            let totalQuestionCount = 15; // Default fallback

            if (isImage) {
                // For images, use the image questions count (total for the image)
                const imageInput = document.getElementById('imageQuestionsCount');
                const inputValue = imageInput ? parseInt(imageInput.value) : null;
                const settingsValue = this.appSettings?.imageQuestionsCount || null;
                totalQuestionCount = inputValue || settingsValue || 5;

                console.log(`🖼️ IMAGE QUESTION COUNT DEBUG:`);
                console.log(`   - Input element value: ${inputValue}`);
                console.log(`   - Settings value: ${settingsValue}`);
                console.log(`   - Final count: ${totalQuestionCount}`);
                console.log(`   - App settings object:`, this.appSettings);
                console.log(`Generating ${totalQuestionCount} questions total for image`);
            } else {
                // For files and text, use the per-page questions count and multiply by page count
                const fileInput = document.getElementById('fileQuestionsCount');
                const inputValue = fileInput ? parseInt(fileInput.value) : null;
                const settingsValue = this.appSettings?.questionsPerPage || null;
                questionsPerPage = inputValue || settingsValue || 5;
                totalQuestionCount = questionsPerPage * pageCount;

                // Apply maximum limit to prevent excessive question generation (silent)
                const maxQuestions = 30; // Maximum for any document
                if (totalQuestionCount > maxQuestions) {
                    totalQuestionCount = maxQuestions;
                }

                console.log(`Generating ${questionsPerPage} questions per page × ${pageCount} pages = ${totalQuestionCount} total questions`);
            }

            // Now update progress with the calculated values
            if (isImage) {
                const estimatedTime = totalQuestionCount <= 5 ? '~10 seconds' : '~15 seconds';
                this.updateProgress(60, `AI generating ${totalQuestionCount} questions from image...`, estimatedTime);
            } else if (pageCount > 1) {
                this.updateProgress(60, `AI generating ${questionsPerPage} questions per page (${pageCount} pages)...`, '~20 seconds');
            } else {
                this.updateProgress(60, `AI generating ${totalQuestionCount} questions...`, '~15 seconds');
            }

            // Get selected model
            const selectedModel = await this.getSelectedModel();
            console.log('Frontend: Selected model for generation:', selectedModel);

            const questions = await window.electronAPI.generateQuestions(
                content,
                this.selectedQuestionType,
                totalQuestionCount,
                selectedModel
            );

            if (questions && questions.length > 0) {
                // Backend has already validated and balanced the questions
                // Just do minimal cleanup without overriding the backend logic
                this.updateProgress(90, 'Finalizing questions...', '~2 seconds');
                this.currentQuestions = this.minimalCleanQuestions(questions);
                this.updateProgress(100, 'Questions generated successfully!', 'Complete!');

                setTimeout(() => {
                    this.displayQuestions();
                }, 1000);
            } else {
                throw new Error('No questions were generated');
            }
        } catch (error) {
            console.error('Question generation error:', error);
            this.handleQuestionGenerationError(error);
        }
    }

    minimalCleanQuestions(questions) {
        // Backend has already done FORCED validation and balancing
        // Frontend should NEVER override backend logic - just display what we get
        console.log('🎯 Frontend received questions from backend:', questions.length);

        return questions.map((question, index) => {
            console.log(`📋 Question ${index + 1}: Answer = ${question.answer}, Type = ${typeof question.answer}`);

            // Clean up the question - TRUST THE BACKEND COMPLETELY
            const cleanedQuestion = { ...question };

            // For True/False questions, ONLY ensure string format (never change the logic)
            if (this.selectedQuestionType === 'TF') {
                // Backend sends boolean or string - convert to display format only
                if (cleanedQuestion.answer === true || cleanedQuestion.answer === 'true' || cleanedQuestion.answer === 'True') {
                    cleanedQuestion.answer = 'True';
                } else if (cleanedQuestion.answer === false || cleanedQuestion.answer === 'false' || cleanedQuestion.answer === 'False') {
                    cleanedQuestion.answer = 'False';
                } else {
                    // Only if completely missing (should never happen with new backend)
                    console.error(`Question ${index + 1} has invalid answer:`, cleanedQuestion.answer);
                    cleanedQuestion.answer = 'True'; // Fallback only
                }

                console.log(`✅ Question ${index + 1} final answer: ${cleanedQuestion.answer}`);
            }

            // Ensure question text exists
            if (!cleanedQuestion.question || cleanedQuestion.question.trim() === '') {
                cleanedQuestion.question = `Question ${index + 1}`;
                console.warn(`Question ${index + 1} has no text, using default`);
            }

            return cleanedQuestion;
        });
    }

    validateAndCleanQuestions(questions) {
        // Legacy function - now redirects to minimal cleanup
        // This preserves compatibility while using the new approach
        return this.minimalCleanQuestions(questions);
    }

    validateTrueFalseLogic(question, questionNumber) {
        if (!question.explanation) return;

        const questionText = question.question.toLowerCase();
        const explanation = question.explanation.toLowerCase();
        const answer = question.answer;

        console.log(`🔍 Validating TF logic for question ${questionNumber}:`);
        console.log(`   Question: "${question.question}"`);
        console.log(`   Answer: ${answer}`);
        console.log(`   Explanation: "${question.explanation}"`);

        // Check for common logic error patterns
        const falseIndicators = [
            'unknown', 'no specific', 'not identifiable', 'not known', 'unclear',
            'cannot be determined', 'no identifiable', 'idiopathic', 'primary',
            'essential', 'no clear cause'
        ];

        const trueIndicators = [
            'specific', 'identifiable', 'known cause', 'secondary', 'caused by',
            'results from', 'due to', 'because of', 'specific etiology'
        ];

        // Medical terminology validation
        this.validateMedicalTerminology(question, questionNumber);

        // Check if explanation contradicts the answer
        let explanationSuggestsFalse = falseIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        let explanationSuggestsTrue = trueIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        // Special case: Primary hypertension questions
        if (questionText.includes('primary') && questionText.includes('hypertension')) {
            if (questionText.includes('specific') || questionText.includes('identifiable')) {
                // Question asks if primary hypertension has specific cause
                // Correct answer should be False (primary = unknown cause)
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Primary hypertension questions about "specific cause" should be FALSE`);
                    console.warn(`   Current answer: ${answer} - Consider changing to FALSE`);

                    // Auto-correct this common error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
        }

        // Special case: Angiotensin II vasodilator/vasoconstrictor questions
        if (questionText.includes('angiotensin ii') || questionText.includes('angiotensin 2')) {
            if (questionText.includes('vasodilator')) {
                // Question incorrectly calls angiotensin II a vasodilator
                // Angiotensin II is a vasoconstrictor, so this should be FALSE
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II is a VASOCONSTRICTOR, not vasodilator`);
                    console.warn(`   Question calls it vasodilator - should be FALSE`);

                    // Auto-correct this medical error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
            if (questionText.includes('vasoconstrictor')) {
                // Question correctly calls angiotensin II a vasoconstrictor
                // This should be TRUE
                if (answer === 'False') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II IS a vasoconstrictor`);
                    console.warn(`   Question correctly identifies it - should be TRUE`);

                    // Auto-correct this medical error
                    question.answer = 'True';
                    console.log(`✅ Auto-corrected answer to: True`);
                }
            }
        }

        // General contradiction check
        if (answer === 'True' && explanationSuggestsFalse && !explanationSuggestsTrue) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is TRUE but explanation suggests FALSE`);
            console.warn(`   False indicators found: ${falseIndicators.filter(i => explanation.includes(i))}`);
        }

        if (answer === 'False' && explanationSuggestsTrue && !explanationSuggestsFalse) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is FALSE but explanation suggests TRUE`);
            console.warn(`   True indicators found: ${trueIndicators.filter(i => explanation.includes(i))}`);
        }
    }

    validateMedicalTerminology(question, questionNumber) {
        const questionText = question.question.toLowerCase();
        const explanation = question.explanation ? question.explanation.toLowerCase() : '';
        const answer = question.answer;

        console.log(`🧠 Universal logic validation for question ${questionNumber}`);

        // Define universal logical fact patterns (works for any profession/subject)
        const universalLogicPatterns = [
            // Medical patterns (keep existing medical knowledge)
            {
                pattern: /angiotensin ii.*vasodilator/i,
                correctAnswer: 'False',
                reason: 'Angiotensin II is a vasoconstrictor, not a vasodilator'
            },
            {
                pattern: /angiotensin ii.*vasoconstrictor/i,
                correctAnswer: 'True',
                reason: 'Angiotensin II is indeed a vasoconstrictor'
            },
            {
                pattern: /ace inhibitors.*increase.*angiotensin ii/i,
                correctAnswer: 'False',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /ace inhibitors.*decrease.*angiotensin ii/i,
                correctAnswer: 'True',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /primary hypertension.*specific.*cause/i,
                correctAnswer: 'False',
                reason: 'Primary hypertension has no specific identifiable cause'
            },
            {
                pattern: /secondary hypertension.*specific.*cause/i,
                correctAnswer: 'True',
                reason: 'Secondary hypertension has specific identifiable causes'
            },
            {
                pattern: /insulin.*decrease.*blood glucose/i,
                correctAnswer: 'True',
                reason: 'Insulin decreases blood glucose levels'
            },
            {
                pattern: /insulin.*increase.*blood glucose/i,
                correctAnswer: 'False',
                reason: 'Insulin decreases, not increases, blood glucose levels'
            },
            {
                pattern: /microbiology.*solely.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle viral, fungal, and parasitic infections, not solely bacterial'
            },
            {
                pattern: /microbiology.*only.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle multiple types of pathogens, not only bacterial'
            },
            {
                pattern: /laboratories.*exclusively.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Medical laboratories handle various types of infections, not exclusively bacterial'
            },

            // Engineering patterns
            {
                pattern: /software engineers.*only.*code/i,
                correctAnswer: 'False',
                reason: 'Software engineers also design, test, debug, document, and maintain systems'
            },
            {
                pattern: /civil engineers.*solely.*buildings/i,
                correctAnswer: 'False',
                reason: 'Civil engineers also work on bridges, roads, dams, and infrastructure systems'
            },

            // Legal patterns
            {
                pattern: /lawyers.*exclusively.*court/i,
                correctAnswer: 'False',
                reason: 'Lawyers also provide legal advice, draft documents, negotiate, and handle transactions'
            },
            {
                pattern: /judges.*only.*criminal cases/i,
                correctAnswer: 'False',
                reason: 'Judges handle criminal, civil, family, and administrative cases'
            },

            // Education patterns
            {
                pattern: /teachers.*solely.*lecture/i,
                correctAnswer: 'False',
                reason: 'Teachers also facilitate discussions, assess students, provide feedback, and mentor'
            },
            {
                pattern: /professors.*only.*research/i,
                correctAnswer: 'False',
                reason: 'Professors also teach, mentor students, serve on committees, and engage in service'
            },

            // Business patterns
            {
                pattern: /accountants.*exclusively.*taxes/i,
                correctAnswer: 'False',
                reason: 'Accountants also handle auditing, financial reporting, budgeting, and advisory services'
            },
            {
                pattern: /managers.*only.*supervise/i,
                correctAnswer: 'False',
                reason: 'Managers also plan, organize, coordinate, and make strategic decisions'
            },

            // Technology patterns
            {
                pattern: /databases.*solely.*store.*data/i,
                correctAnswer: 'False',
                reason: 'Databases also retrieve, organize, secure, backup, and process data'
            },
            {
                pattern: /firewalls.*only.*block/i,
                correctAnswer: 'False',
                reason: 'Firewalls also monitor, log, filter, and allow authorized traffic'
            }
        ];

        // Check each universal logic pattern
        for (const fact of universalLogicPatterns) {
            if (fact.pattern.test(questionText)) {
                console.log(`   📋 Medical pattern matched: ${fact.pattern}`);
                console.log(`   📚 Medical fact: ${fact.reason}`);
                console.log(`   🎯 Expected answer: ${fact.correctAnswer}`);
                console.log(`   🤖 AI answer: ${answer}`);

                if (answer !== fact.correctAnswer) {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Pattern: ${fact.pattern}`);
                    console.warn(`   Logic rule: ${fact.reason}`);
                    console.warn(`   AI gave: ${answer}, should be: ${fact.correctAnswer}`);

                    // Auto-correct logic errors
                    question.answer = fact.correctAnswer;
                    console.log(`✅ Auto-corrected logic error: ${answer} → ${fact.correctAnswer}`);

                    // Update explanation if it contradicts the correction
                    if (question.explanation && !question.explanation.toLowerCase().includes(fact.reason.toLowerCase())) {
                        question.explanation += ` (Note: ${fact.reason})`;
                        console.log(`📝 Enhanced explanation with logical fact`);
                    }
                }
                break; // Only apply first matching pattern
            }
        }

        // Check for explanation contradictions
        if (explanation) {
            // If explanation mentions "vasoconstrictor" but question says "vasodilator"
            if (questionText.includes('vasodilator') && explanation.includes('vasoconstrictor')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasodilator' but explanation says 'vasoconstrictor'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // If explanation mentions "vasodilator" but question says "vasoconstrictor"
            if (questionText.includes('vasoconstrictor') && explanation.includes('vasodilator')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasoconstrictor' but explanation says 'vasodilator'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // Universal Logic Contradiction Detection
            this.detectUniversalContradictions(question, questionNumber, questionText, explanation, answer);
        }
    }

    showProcessingScreen() {
        this.showScreen('processingScreen');
        this.updateProgress(0, 'Initializing AI models...', 'Calculating...');
    }

    updateProgress(percentage, status, eta = null) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const processingStatus = document.getElementById('processingStatus');
        const progressETA = document.getElementById('progressETA');

        if (!progressFill || !progressText || !processingStatus) {
            console.error('Progress elements not found!');
            return;
        }

        // Update progress bar
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${percentage}%`;
        processingStatus.textContent = status;

        // Update ETA if provided
        if (progressETA && eta) {
            progressETA.textContent = eta;
        }

        // Update processing steps based on percentage
        this.updateProcessingSteps(percentage);

        // Force a repaint to ensure the progress bar updates
        progressFill.offsetHeight;
    }

    updateProcessingSteps(percentage) {
        const steps = ['step1', 'step2', 'step3', 'step4'];
        const stepPercentages = [25, 50, 75, 100];

        steps.forEach((stepId, index) => {
            const stepElement = document.getElementById(stepId);
            if (!stepElement) return;

            stepElement.classList.remove('active', 'completed');

            if (percentage >= stepPercentages[index]) {
                stepElement.classList.add('completed');
            } else if (percentage >= (stepPercentages[index] - 25)) {
                stepElement.classList.add('active');
            }
        });
    }

    handleQuestionGenerationError(error) {
        const errorMessage = error.message || 'Unknown error occurred';

        // Check for specific error types
        if (errorMessage.includes('Rate limit exceeded')) {
            this.handleRateLimitError();
        } else if (errorMessage.includes('Failed to generate questions after')) {
            this.handleGenerationFailure(error);
        } else if (errorMessage.includes('Network')) {
            this.handleNetworkError();
        } else {
            this.handleGenericError(error);
        }
    }

    handleRateLimitError() {
        this.updateProgress(0, 'API rate limit reached. Retrying with different models...', 'Please wait...');

        // Show retry options
        this.showRateLimitDialog();
    }

    handleGenerationFailure(error) {
        this.updateProgress(0, 'Question generation failed. Checking alternatives...', 'Retrying...');

        // Show retry with options
        this.showGenerationFailureDialog(error);
    }

    handleNetworkError() {
        this.updateProgress(0, 'Network connection issue detected...', 'Check connection');

        this.showNotification('Network error. Please check your internet connection and try again.', 'error');
        setTimeout(() => {
            this.showScreen('contentScreen');
        }, 3000);
    }

    handleGenericError(error) {
        this.updateProgress(0, 'An unexpected error occurred...', 'Error');

        console.error('Generic error:', error);
        this.showNotification(`Error: ${error.message}`, 'error');
        setTimeout(() => {
            this.showScreen('contentScreen');
        }, 2000);
    }

    showRateLimitDialog() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Create a custom dialog for rate limit handling
        const dialog = document.createElement('div');
        dialog.className = 'error-dialog rate-limit-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>${isArabic ? 'تم الوصول إلى حد معدل API' : 'API Rate Limit Reached'}</h3>
                <p>${isArabic ? 'تواجه خدمة الذكاء الاصطناعي حالياً طلباً عالياً. جميع النماذج المتاحة محدودة المعدل مؤقتاً.' : 'The AI service is currently experiencing high demand. All available models are temporarily rate-limited.'}</p>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="tryDifferentModelBtn">
                        <i class="fas fa-exchange-alt"></i> ${isArabic ? 'جرب نموذج مختلف' : 'Try Different Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Add event listeners
        dialog.querySelector('#tryDifferentModelBtn').addEventListener('click', async () => await this.showModelSelectionDialog());
        dialog.querySelector('#cancelBtn').addEventListener('click', () => this.cancelGeneration());
    }

    showGenerationFailureDialog(error) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const dialog = document.createElement('div');
        dialog.className = 'error-dialog generation-failure-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>${isArabic ? 'فشل في إنشاء الأسئلة' : 'Question Generation Failed'}</h3>
                <p>${isArabic ? 'واجهنا مشاكل في إنشاء الأسئلة. قد يكون هذا بسبب:' : 'We encountered issues generating questions. This might be due to:'}</p>
                <ul>
                    <li>${isArabic ? 'طلب عالي على API' : 'High API demand'}</li>
                    <li>${isArabic ? 'محتوى معقد يتطلب وقت معالجة أكثر' : 'Complex content requiring more processing time'}</li>
                    <li>${isArabic ? 'قيود خدمة مؤقتة' : 'Temporary service limitations'}</li>
                </ul>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="retrySelectedBtn">
                        <i class="fas fa-redo"></i> ${isArabic ? 'حاول مرة أخرى' : 'Try Again'}
                    </button>
                    <button class="btn btn-secondary" id="tryDifferentBtn">
                        <i class="fas fa-exchange-alt"></i> ${isArabic ? 'جرب نموذج مختلف' : 'Try Different Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelFailureBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Add event listeners
        dialog.querySelector('#retrySelectedBtn').addEventListener('click', () => this.retryWithSelectedModel());
        dialog.querySelector('#tryDifferentBtn').addEventListener('click', async () => await this.showModelSelectionDialog());
        dialog.querySelector('#cancelFailureBtn').addEventListener('click', () => this.cancelGeneration());
    }





    async showModelSelectionDialog() {
        this.closeErrorDialogs();

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const dialog = document.createElement('div');
        dialog.className = 'error-dialog model-selection-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>${isArabic ? 'اختر نموذج الذكاء الاصطناعي' : 'Select AI Model'}</h3>
                <p>${isArabic ? 'اختر نموذج الذكاء الاصطناعي الذي تريد تجربته لإنشاء الأسئلة:' : 'Choose which AI model you\'d like to try for generating questions:'}</p>
                <div class="model-selection-container">
                    <label for="dialogModelSelect">${isArabic ? 'نموذج الذكاء الاصطناعي:' : 'AI Model:'}</label>
                    <select id="dialogModelSelect" class="setting-select">
                        <option value="auto">${isArabic ? 'تلقائي (الأفضل المتاح)' : 'Auto (Best Available)'}</option>
                        <!-- Models will be loaded dynamically -->
                    </select>
                </div>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="trySelectedModelBtn">
                        <i class="fas fa-play"></i> ${isArabic ? 'جرب النموذج المحدد' : 'Try Selected Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelModelSelectionBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Load available models into the dialog dropdown and set current selection
        await this.loadModelsIntoDialog(dialog);

        // Add event listeners
        dialog.querySelector('#trySelectedModelBtn').addEventListener('click', () => this.trySelectedModelFromDialog());
        dialog.querySelector('#cancelModelSelectionBtn').addEventListener('click', () => this.cancelGeneration());
    }

    async loadModelsIntoDialog(dialog) {
        const dialogSelect = dialog.querySelector('#dialogModelSelect');
        if (!dialogSelect) return;

        try {
            // Get all models from backend
            const result = await window.electronAPI.getAllModels();
            console.log('Models loaded for dialog:', result);

            if (result.success && result.models && result.models.length > 0) {
                // Add all available models to the dialog dropdown
                result.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name || model.id;
                    dialogSelect.appendChild(option);
                });

                // Set current model as selected
                const currentModel = await this.getSelectedModel();
                dialogSelect.value = currentModel;
            } else {
                // No custom models available - add a helpful message
                const currentLanguage = localStorage.getItem('language') || 'en';
                const isArabic = currentLanguage === 'ar';

                const noModelsOption = document.createElement('option');
                noModelsOption.value = '';
                noModelsOption.textContent = isArabic ? 'يجب إضافة نماذج أولاً من إعدادات النماذج' : 'Please add models first from Model Management';
                noModelsOption.disabled = true;
                dialogSelect.appendChild(noModelsOption);

                // Disable the try button since no models are available
                const tryBtn = dialog.querySelector('#trySelectedModelBtn');
                if (tryBtn) {
                    tryBtn.disabled = true;
                    tryBtn.title = isArabic ? 'لا توجد نماذج متاحة' : 'No models available';
                }

                console.log('No custom models found - user needs to add models first');
            }
        } catch (error) {
            console.error('Error loading models into dialog:', error);
        }
    }

    async trySelectedModelFromDialog() {
        const dialogSelect = document.getElementById('dialogModelSelect');
        const selectedModel = dialogSelect.value;

        this.closeErrorDialogs();

        // Update the main model selection dropdown
        const modelSelect = document.getElementById('modelSelect');
        if (modelSelect) {
            modelSelect.value = selectedModel;
            await this.saveModelPreference(selectedModel);
        }

        const modelName = this.getModelDisplayName(selectedModel);
        this.updateProgress(10, `Trying with ${modelName}...`, 'Switching models...');

        try {
            if (this.lastGenerationParams) {
                await this.generateQuestions(
                    this.lastGenerationParams.content,
                    this.lastGenerationParams.isImage,
                    this.lastGenerationParams.pageCount
                );
            }
        } catch (error) {
            this.handleQuestionGenerationError(error);
        }
    }





    cancelGeneration() {
        console.log('Cancel generation called');

        this.closeErrorDialogs();

        // Reset progress
        this.updateProgress(0, 'Generation canceled', 'Canceled');

        // Show notification and return to content screen
        this.showNotification('Question generation canceled', 'info');

        // Small delay to show the canceled status
        setTimeout(() => {
            this.showScreen('contentScreen');
        }, 1000);
    }

    closeErrorDialogs() {
        const dialogs = document.querySelectorAll('.error-dialog');
        dialogs.forEach(dialog => dialog.remove());
    }

    getQuestionCount() {
        // Get the current question count setting
        if (this.appSettings?.questionsPerPage) {
            return this.appSettings.questionsPerPage;
        }

        // Fallback to default
        return 10;
    }

    initializeModelSelection() {
        console.log('initializeModelSelection() called');
        const modelSelect = document.getElementById('modelSelect');
        if (!modelSelect) {
            console.log('Model select element not found!');
            return;
        }

        console.log('Model select element found, current value:', modelSelect.value);

        // Load models dynamically from backend
        this.refreshModelDropdown();

        // Load saved model preference
        this.loadModelPreference();

        // Set up change listener
        modelSelect.addEventListener('change', () => {
            console.log('Model selection changed to:', modelSelect.value);
            this.saveModelPreference(modelSelect.value);
            this.updateModelStatus(modelSelect.value);
        });

        // Initial status check
        this.updateModelStatus(modelSelect.value);

    }

    async loadModelPreference() {
        try {
            if (!window.electronAPI || !window.electronAPI.getSettings) {
                console.warn('electronAPI not ready for model settings');
                return;
            }

            console.log('Loading model preference...');
            const result = await window.electronAPI.getSettings();
            console.log('Settings result:', result);

            if (result.success && result.settings) {
                console.log('Settings loaded:', result.settings);

                // Store the preference in class variable
                if (result.settings.preferredModel) {
                    this.savedModelPreference = result.settings.preferredModel;
                    console.log('Stored model preference:', this.savedModelPreference);

                    // Set the dropdown value
                    const modelSelect = document.getElementById('modelSelect');
                    if (modelSelect) {
                        console.log('Setting model select to:', result.settings.preferredModel);
                        modelSelect.value = result.settings.preferredModel;
                        console.log('Model select value after setting:', modelSelect.value);

                        // Trigger change event to update status
                        this.updateModelStatus(result.settings.preferredModel);
                    } else {
                        console.log('modelSelect element not found');
                    }
                } else {
                    console.log('No preferredModel in settings');
                }
            } else {
                console.log('Failed to load settings or settings empty');
            }
        } catch (error) {
            console.warn('Could not load model preference:', error);
        }
    }

    async saveModelPreference(model) {
        try {
            if (!window.electronAPI || !window.electronAPI.saveSettings) {
                console.warn('electronAPI not ready for saving model preference');
                return;
            }

            // Save just the model preference
            const result = await window.electronAPI.saveSettings({ preferredModel: model });
            if (result.success) {
                console.log('Model preference saved:', model);
                this.showNotification(`AI model updated to ${this.getModelDisplayName(model)}`, 'success');
            } else {
                this.showNotification('Failed to save model preference', 'error');
            }
        } catch (error) {
            console.error('Error saving model preference:', error);
            this.showNotification('Error saving model preference', 'error');
        }
    }

    getModelDisplayName(modelId) {
        // Only handle special cases, let custom models use their configured names
        if (modelId === 'auto') {
            return 'Auto (Best Available)';
        }

        // For all other models, return the ID as-is or get from backend
        return modelId;
    }

    async updateModelStatus(modelId) {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        const statusDescription = document.querySelector('.status-description');

        if (!statusIndicator || !statusText) return;

        // Get current language for direct translations
        const currentLanguage = localStorage.getItem('language') || 'en';

        if (modelId === 'auto') {
            statusIndicator.className = 'status-indicator status-available';
            const autoText = currentLanguage === 'ar' ? 'تلقائي (الأفضل المتاح)' : 'Auto (Best Available)';
            const availableText = currentLanguage === 'ar' ? 'النموذج متاح' : 'Model available';

            statusText.textContent = autoText;
            if (statusDescription) {
                statusDescription.textContent = availableText;
            }
        } else if (modelId) {
            // Show checking status first
            statusIndicator.className = 'status-indicator status-unknown';
            const checkingText = currentLanguage === 'ar' ? 'فحص التوفر...' : 'Checking availability...';
            const willAppearText = currentLanguage === 'ar' ? 'ستظهر حالة النموذج هنا' : 'Model status will appear here';

            statusText.textContent = checkingText;
            if (statusDescription) {
                statusDescription.textContent = willAppearText;
            }

            // Check if model is rate limited
            try {
                const rateLimitResult = await window.electronAPI.getRateLimitedModels();
                if (rateLimitResult.success) {
                    const rateLimitedModel = rateLimitResult.models.find(m => m.model === modelId);
                    if (rateLimitedModel) {
                        // Model is rate limited
                        statusIndicator.className = 'status-indicator status-rate-limited';

                        // Get current language
                        const currentLanguage = localStorage.getItem('language') || 'en';
                        const rateLimitText = currentLanguage === 'ar' ? 'محدود المعدل' : 'Rate Limited';
                        const unavailableText = currentLanguage === 'ar' ? 'النموذج غير متاح مؤقتاً بسبب حدود المعدل' : 'Model temporarily unavailable due to rate limits';

                        statusText.textContent = `${rateLimitText} (${rateLimitedModel.timeAgo}m ago)`;
                        if (statusDescription) {
                            statusDescription.textContent = unavailableText;
                        }
                        return;
                    }
                }
            } catch (error) {
                console.warn('Could not check rate limit status:', error);
            }

            // Model appears to be available (not rate limited)
            statusIndicator.className = 'status-indicator status-available';
            const availableText = currentLanguage === 'ar' ? 'النموذج متاح' : 'Model available';

            // Check if we have recent test results for this model
            const lastTestResult = this.getLastTestResult(modelId);
            if (lastTestResult) {
                const statusWithTiming = currentLanguage === 'ar' ?
                    `النموذج متاح (${lastTestResult.duration}ms)` :
                    `Model available (${lastTestResult.duration}ms)`;
                statusText.textContent = statusWithTiming;

                if (statusDescription) {
                    const successText = currentLanguage === 'ar' ?
                        `آخر اختبار: ${lastTestResult.questions} أسئلة في ${lastTestResult.duration}ms` :
                        `Last test: ${lastTestResult.questions} questions in ${lastTestResult.duration}ms`;
                    statusDescription.textContent = successText;
                }
            } else {
                statusText.textContent = availableText;
                if (statusDescription) {
                    statusDescription.textContent = availableText;
                }
            }
        } else {
            // No model selected
            statusIndicator.className = 'status-indicator status-unknown';
            const checkingText = currentLanguage === 'ar' ? 'فحص التوفر...' : 'Checking availability...';
            const willAppearText = currentLanguage === 'ar' ? 'ستظهر حالة النموذج هنا' : 'Model status will appear here';

            statusText.textContent = checkingText;
            if (statusDescription) {
                statusDescription.textContent = willAppearText;
            }
        }
    }

    getTranslationText(key) {
        // Get current language from localStorage or default to English
        const currentLanguage = localStorage.getItem('language') || 'en';

        // Access the global translations object
        if (window.translations && window.translations[currentLanguage] && window.translations[currentLanguage][key]) {
            return window.translations[currentLanguage][key];
        }

        // Fallback to English if translation not found
        if (window.translations && window.translations['en'] && window.translations['en'][key]) {
            return window.translations['en'][key];
        }

        // Final fallback to the key itself
        return key;
    }

    getLastTestResult(modelId) {
        // Check if we have stored test results for this model
        if (this.testResults && Array.isArray(this.testResults)) {
            const modelResult = this.testResults.find(result => result.model === modelId);
            if (modelResult && modelResult.success) {
                return {
                    duration: modelResult.duration,
                    questions: modelResult.questionsGenerated || 0
                };
            }
        }
        return null;
    }



    async getSelectedModel() {
        // First, try to get the current dropdown value
        const modelSelect = document.getElementById('modelSelect');
        const dropdownValue = modelSelect ? modelSelect.value : 'auto';

        // If dropdown has a specific model selected, use it
        if (dropdownValue && dropdownValue !== 'auto') {
            console.log('getSelectedModel() using dropdown value:', dropdownValue);
            return dropdownValue;
        }

        // If dropdown is auto, try to load from settings directly
        try {
            if (window.electronAPI && window.electronAPI.getSettings) {
                const result = await window.electronAPI.getSettings();
                if (result.success && result.settings && result.settings.preferredModel && result.settings.preferredModel !== 'auto') {
                    console.log('getSelectedModel() using settings preference:', result.settings.preferredModel);
                    return result.settings.preferredModel;
                }
            }
        } catch (error) {
            console.warn('Could not load settings in getSelectedModel:', error);
        }

        // Check saved preference variable as fallback
        if (this.savedModelPreference && this.savedModelPreference !== 'auto') {
            console.log('getSelectedModel() using saved preference:', this.savedModelPreference);
            return this.savedModelPreference;
        }

        // Default to auto
        console.log('getSelectedModel() defaulting to auto');
        return 'auto';
    }

    // Model Management Functions
    initializeModelManagement() {
        console.log('Initializing model management...');

        // Add Model Button
        const addModelBtn = document.getElementById('addModelBtn');
        if (addModelBtn) {
            addModelBtn.addEventListener('click', () => this.showAddModelDialog());
        }

        // Remove Model Button
        const removeModelBtn = document.getElementById('removeModelBtn');
        if (removeModelBtn) {
            removeModelBtn.addEventListener('click', () => this.showRemoveModelDialog());
        }



        // Test Models Button
        const testModelsBtn = document.getElementById('testModelsBtn');
        if (testModelsBtn) {
            testModelsBtn.addEventListener('click', () => this.showTestModelsDialog());
        }

        // API Key Management Button
        const manageApiKeyBtn = document.getElementById('manageApiKeyBtn');
        if (manageApiKeyBtn) {
            manageApiKeyBtn.addEventListener('click', () => this.openApiKeyManager());
        }

        // Add Model Dialog Events
        this.setupAddModelDialog();

        // Remove Model Dialog Events
        this.setupRemoveModelDialog();

        // Test Models Dialog Events
        this.setupTestModelsDialog();
    }

    setupAddModelDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeAddModelDialog');
        const cancelBtn = document.getElementById('cancelAddModel');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideAddModelDialog());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hideAddModelDialog());

        // Confirm add model
        const confirmBtn = document.getElementById('confirmAddModel');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.addNewModel());
        }

        // Close on overlay click
        const overlay = document.getElementById('addModelDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideAddModelDialog();
            });
        }
    }

    setupRemoveModelDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeRemoveModelDialog');
        const cancelBtn = document.getElementById('cancelRemoveModel');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideRemoveModelDialog());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hideRemoveModelDialog());

        // Model selection change
        const selectElement = document.getElementById('removeModelSelect');
        if (selectElement) {
            selectElement.addEventListener('change', () => this.updateRemoveModelInfo());
        }

        // Confirm remove model
        const confirmBtn = document.getElementById('confirmRemoveModel');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.removeSelectedModel());
        }

        // Close on overlay click
        const overlay = document.getElementById('removeModelDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideRemoveModelDialog();
            });
        }
    }



    // Dialog Show/Hide Methods
    showAddModelDialog() {
        const dialog = document.getElementById('addModelDialog');
        if (dialog) {
            // Clear form
            document.getElementById('newModelId').value = '';
            document.getElementById('newModelName').value = '';
            document.getElementById('newModelDescription').value = '';

            dialog.style.display = 'flex';

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }

            // Focus on first input
            setTimeout(() => {
                document.getElementById('newModelId').focus();
            }, 100);
        }
    }

    hideAddModelDialog() {
        const dialog = document.getElementById('addModelDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    showRemoveModelDialog() {
        const dialog = document.getElementById('removeModelDialog');
        if (dialog) {
            // Populate model list
            this.populateRemoveModelList();
            dialog.style.display = 'flex';

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideRemoveModelDialog() {
        const dialog = document.getElementById('removeModelDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }



    // Model Management Core Functions
    async addNewModel() {
        const modelId = document.getElementById('newModelId').value.trim();
        const modelName = document.getElementById('newModelName').value.trim();
        const modelDescription = document.getElementById('newModelDescription').value.trim();

        // Validation
        if (!modelId) {
            this.showNotification('Please enter a model ID', 'error');
            return;
        }

        if (!modelName) {
            this.showNotification('Please enter a display name', 'error');
            return;
        }

        // Validate model ID format (basic check)
        if (!modelId.includes('/') || !modelId.includes(':')) {
            this.showNotification('Model ID should be in format: provider/model-name:tier', 'error');
            return;
        }

        try {
            // Call backend to add model
            const result = await window.electronAPI.addModel({
                id: modelId,
                name: modelName,
                description: modelDescription
            });

            if (result.success) {
                this.showNotification('Model added successfully!', 'success');
                this.hideAddModelDialog();

                // Refresh model dropdown
                await this.refreshModelDropdown();
            } else {
                this.showNotification(result.error || 'Failed to add model', 'error');
            }
        } catch (error) {
            console.error('Error adding model:', error);
            this.showNotification('Error adding model: ' + error.message, 'error');
        }
    }

    async populateRemoveModelList() {
        const selectElement = document.getElementById('removeModelSelect');
        if (!selectElement) return;

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Choose a model to remove...</option>';

        try {
            // Get all models from backend
            const result = await window.electronAPI.getAllModels();

            if (result.success && result.models) {
                // Show ALL models - no restrictions
                const allModels = result.models;

                if (allModels.length === 0) {
                    const noModelsOption = document.createElement('option');
                    noModelsOption.value = '';
                    noModelsOption.textContent = 'No models available to remove';
                    noModelsOption.disabled = true;
                    selectElement.appendChild(noModelsOption);
                } else {
                    allModels.forEach(model => {
                        const newOption = document.createElement('option');
                        newOption.value = model.id;
                        newOption.textContent = `${model.name || model.id}${model.custom ? ' (Custom)' : ''}`;
                        selectElement.appendChild(newOption);
                    });
                }
            }
        } catch (error) {
            console.error('Error loading models for removal:', error);
            const errorOption = document.createElement('option');
            errorOption.value = '';
            errorOption.textContent = 'Error loading models';
            errorOption.disabled = true;
            selectElement.appendChild(errorOption);
        }

        // Reset UI
        this.updateRemoveModelInfo();
    }

    updateRemoveModelInfo() {
        const selectElement = document.getElementById('removeModelSelect');
        const infoDiv = document.getElementById('removeModelInfo');
        const confirmBtn = document.getElementById('confirmRemoveModel');

        if (!selectElement || !infoDiv || !confirmBtn) return;

        const selectedValue = selectElement.value;

        if (selectedValue) {
            // Show model info
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            document.getElementById('removeModelId').textContent = selectedValue;
            document.getElementById('removeModelName').textContent = selectedOption.textContent;

            infoDiv.style.display = 'block';
            confirmBtn.disabled = false;
        } else {
            // Hide model info
            infoDiv.style.display = 'none';
            confirmBtn.disabled = true;
        }
    }

    async removeSelectedModel() {
        const selectElement = document.getElementById('removeModelSelect');
        if (!selectElement || !selectElement.value) return;

        const modelId = selectElement.value;
        const modelName = selectElement.options[selectElement.selectedIndex].textContent;

        // Confirm deletion
        const confirmed = confirm(`Are you sure you want to remove the model "${modelName}"?\n\nThis action cannot be undone.`);
        if (!confirmed) return;

        try {
            // Call backend to remove model
            const result = await window.electronAPI.removeModel(modelId);

            if (result.success) {
                this.showNotification('Model removed successfully!', 'success');
                this.hideRemoveModelDialog();

                // Refresh model dropdown
                await this.refreshModelDropdown();
            } else {
                this.showNotification(result.error || 'Failed to remove model', 'error');
            }
        } catch (error) {
            console.error('Error removing model:', error);
            this.showNotification('Error removing model: ' + error.message, 'error');
        }
    }



    async refreshModelDropdown() {
        try {
            // Get updated models from backend
            const result = await window.electronAPI.getAllModels();

            if (result.success && result.models) {
                const modelSelect = document.getElementById('modelSelect');
                if (modelSelect) {
                    // Save current selection
                    const currentValue = modelSelect.value;

                    // Clear existing options except auto
                    modelSelect.innerHTML = '<option value="auto">Auto (Best Available)</option>';

                    // Add all available models (no distinction between default/custom)
                    result.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.name || model.id;
                        modelSelect.appendChild(option);
                    });

                    // Restore selection if still available
                    if (currentValue && Array.from(modelSelect.options).some(opt => opt.value === currentValue)) {
                        modelSelect.value = currentValue;
                    }
                }
            }
        } catch (error) {
            console.error('Error refreshing model dropdown:', error);
        }
    }

    setupTestModelsDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeTestModelsDialog');
        const closeBtn2 = document.getElementById('closeTestModels');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideTestModelsDialog());
        if (closeBtn2) closeBtn2.addEventListener('click', () => this.hideTestModelsDialog());

        // Start testing
        const startBtn = document.getElementById('startModelTest');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.startModelTesting());
        }

        // Stop testing
        const stopBtn = document.getElementById('stopModelTest');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopModelTesting());
        }

        // Clear rate limits
        const clearRateLimitsBtn = document.getElementById('clearRateLimits');
        if (clearRateLimitsBtn) {
            clearRateLimitsBtn.addEventListener('click', () => this.clearAllRateLimits());
        }

        // Export results
        const exportBtn = document.getElementById('exportTestResults');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportTestResults());
        }

        // Close on overlay click
        const overlay = document.getElementById('testModelsDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideTestModelsDialog();
            });
        }
    }

    showTestModelsDialog() {
        const dialog = document.getElementById('testModelsDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            this.resetTestResults();

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideTestModelsDialog() {
        const dialog = document.getElementById('testModelsDialog');
        if (dialog) {
            dialog.style.display = 'none';
            this.stopModelTesting();
        }
    }

    // API Key Manager
    async openApiKeyManager() {
        console.log('Opening API Key Manager...');

        try {
            // Show the dialog
            this.showApiKeyDialog();

            // Load current API key info
            await this.loadApiKeyInfo();

        } catch (error) {
            console.error('Error opening API key manager:', error);
            this.showAlert('error', `Failed to open API key manager: ${error.message}`);
        }
    }

    showApiKeyDialog() {
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            this.setupApiKeyDialogEvents();

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideApiKeyDialog() {
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.style.display = 'none';
            this.clearApiKeyAlert();
        }
    }

    setupApiKeyDialogEvents() {
        // Close button events
        const closeBtn = document.getElementById('closeApiKeyDialog');
        const cancelBtn = document.getElementById('cancelApiKeyDialog');

        if (closeBtn) {
            closeBtn.onclick = () => this.hideApiKeyDialog();
        }

        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideApiKeyDialog();
        }

        // Update API key button
        const updateBtn = document.getElementById('updateApiKeyBtn');
        if (updateBtn) {
            updateBtn.onclick = () => this.updateApiKey();
        }

        // Test API key button
        const testBtn = document.getElementById('testApiKeyBtn');
        if (testBtn) {
            testBtn.onclick = () => this.testApiKey();
        }

        // Toggle visibility button
        const toggleBtn = document.getElementById('toggleApiKeyVisibility');
        if (toggleBtn) {
            toggleBtn.onclick = () => this.toggleApiKeyVisibility();
        }

        // Input validation
        const input = document.getElementById('newApiKeyInput');
        if (input) {
            input.oninput = () => this.validateApiKeyInput();
            input.onkeypress = (e) => {
                if (e.key === 'Enter') {
                    this.updateApiKey();
                }
            };
        }

        // Close on overlay click
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.onclick = (e) => {
                if (e.target === dialog) {
                    this.hideApiKeyDialog();
                }
            };
        }
    }

    async loadApiKeyInfo() {
        try {
            const container = document.getElementById('currentApiKeyInfo');
            if (!container) return;

            container.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading API key information...
                </div>
            `;

            const result = await window.electronAPI.getApiKeyInfo();

            if (result.success && result.hasKey) {
                container.innerHTML = `
                    <div class="api-key-info-grid">
                        <div class="api-key-info-label">Status:</div>
                        <div class="api-key-info-value">
                            <span class="status-indicator ${result.isValidFormat ? 'status-available' : 'status-unavailable'}"></span>
                            ${result.isValidFormat ? 'Valid Format' : 'Invalid Format'}
                        </div>

                        <div class="api-key-info-label">Provider:</div>
                        <div class="api-key-info-value">${result.provider}</div>

                        <div class="api-key-info-label">Key (Masked):</div>
                        <div class="api-key-info-value">${result.maskedKey}</div>

                        <div class="api-key-info-label">Length:</div>
                        <div class="api-key-info-value">${result.keyLength} characters</div>
                    </div>
                `;

                if (!result.isValidFormat) {
                    this.showApiKeyAlert('warning', 'Your current API key format is invalid. Please update it with a valid OpenRouter key.');
                }
            } else {
                container.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>No API Key Found</strong><br>
                        Please add an OpenRouter API key to use the question generation features.
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading API key info:', error);
            const container = document.getElementById('currentApiKeyInfo');
            if (container) {
                container.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
    }

    async updateApiKey() {
        const input = document.getElementById('newApiKeyInput');
        const updateBtn = document.getElementById('updateApiKeyBtn');

        if (!input || !updateBtn) return;

        const newKey = input.value.trim();

        if (!newKey) {
            this.showApiKeyAlert('error', 'Please enter an API key.');
            return;
        }

        // Validate format
        if (!newKey.startsWith('sk-or-v1-')) {
            this.showApiKeyAlert('error', 'API key must start with "sk-or-v1-"');
            return;
        }

        if (newKey.length !== 73) {
            this.showApiKeyAlert('error', 'API key must be exactly 73 characters long.');
            return;
        }

        // Show loading state
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

        try {
            const result = await window.electronAPI.updateApiKey(newKey);

            if (result.success) {
                this.showApiKeyAlert('success', result.message);
                input.value = '';
                await this.loadApiKeyInfo(); // Refresh the current key info
            } else {
                this.showApiKeyAlert('error', result.error);
            }
        } catch (error) {
            console.error('Error updating API key:', error);
            this.showApiKeyAlert('error', `Failed to update API key: ${error.message}`);
        } finally {
            updateBtn.disabled = false;
            updateBtn.innerHTML = '<i class="fas fa-save"></i> Update API Key';
        }
    }

    async testApiKey() {
        const testBtn = document.getElementById('testApiKeyBtn');
        const testResults = document.getElementById('apiKeyTestResults');
        const testContent = document.getElementById('testResultsContent');

        if (!testBtn || !testResults || !testContent) return;

        // Show loading state
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        testResults.style.display = 'block';
        testContent.innerHTML = `
            <div class="api-key-alert info">
                <i class="fas fa-spinner fa-spin"></i> Testing API key with sample request...
            </div>
        `;

        try {
            const result = await window.electronAPI.testApiKey();

            if (result.success) {
                testContent.innerHTML = `
                    <div class="api-key-alert success">
                        <strong>✅ API Key Test Successful!</strong><br>
                        ${result.message}<br>
                        Questions generated: ${result.questionsGenerated}
                    </div>
                `;
            } else {
                testContent.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>❌ API Key Test Failed</strong><br>
                        ${result.error}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error testing API key:', error);
            testContent.innerHTML = `
                <div class="api-key-alert error">
                    <strong>❌ Test Error</strong><br>
                    ${error.message}
                </div>
            `;
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-flask"></i> Test Current Key';
        }
    }

    toggleApiKeyVisibility() {
        const input = document.getElementById('newApiKeyInput');
        const button = document.getElementById('toggleApiKeyVisibility');

        if (!input || !button) return;

        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    validateApiKeyInput() {
        const input = document.getElementById('newApiKeyInput');
        if (!input) return;

        const value = input.value;

        // Remove existing classes
        input.classList.remove('valid', 'invalid');

        if (value.length === 0) {
            // No styling for empty input
        } else if (value.startsWith('sk-or-v1-') && value.length === 73) {
            input.classList.add('valid');
        } else {
            input.classList.add('invalid');
        }
    }

    showApiKeyAlert(type, message) {
        const container = document.getElementById('apiKeyAlertContainer');
        if (!container) return;

        const alertId = 'api-key-alert-' + Date.now();

        const alertHtml = `
            <div id="${alertId}" class="api-key-alert ${type}">
                ${message}
            </div>
        `;

        container.innerHTML = alertHtml;

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }
    }

    clearApiKeyAlert() {
        const container = document.getElementById('apiKeyAlertContainer');
        if (container) {
            container.innerHTML = '';
        }
    }

    resetTestResults() {
        const resultsContainer = document.getElementById('testResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="test-placeholder">
                    <i class="fas fa-flask"></i>
                    <p>Click "Start Testing" to test all available models</p>
                </div>
            `;
        }

        // Hide export button
        const exportBtn = document.getElementById('exportTestResults');
        if (exportBtn) exportBtn.style.display = 'none';

        // Reset testing state
        this.testingInProgress = false;
        this.testResults = [];
    }

    async startModelTesting() {
        if (this.testingInProgress) return;

        this.testingInProgress = true;
        this.testResults = [];

        // Get test parameters
        const content = document.getElementById('testContent').value.trim();
        const questionType = document.getElementById('testQuestionType').value;
        const questionCount = parseInt(document.getElementById('testQuestionCount').value) || 3;

        if (!content) {
            this.showNotification('Please enter test content', 'error');
            this.testingInProgress = false;
            return;
        }

        // Update UI
        const startBtn = document.getElementById('startModelTest');
        const stopBtn = document.getElementById('stopModelTest');
        if (startBtn) startBtn.style.display = 'none';
        if (stopBtn) stopBtn.style.display = 'inline-flex';

        try {
            // Get all available models
            const result = await window.electronAPI.getAllModels();
            if (!result.success || !result.models) {
                throw new Error('Failed to get models list');
            }

            const models = result.models;
            this.setupTestResultsDisplay(models);

            // Test each model sequentially
            for (let i = 0; i < models.length && this.testingInProgress; i++) {
                const model = models[i];
                await this.testSingleModel(model, content, questionType, questionCount, i);

                // Small delay between tests
                if (this.testingInProgress) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

        } catch (error) {
            console.error('Error during model testing:', error);
            this.showNotification('Error during testing: ' + error.message, 'error');
        } finally {
            this.finishModelTesting();
        }
    }

    setupTestResultsDisplay(models) {
        const resultsContainer = document.getElementById('testResults');
        if (!resultsContainer) return;

        const modelsHtml = models.map((model, index) => `
            <div class="test-item" id="test-item-${index}">
                <div class="test-model-info">
                    <div class="test-model-name">${model.name || model.id}</div>
                    <div class="test-model-id">${model.id}</div>
                </div>
                <div class="test-status">
                    <div class="test-status-icon test-status-pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <span class="test-timing">Pending...</span>
                </div>
            </div>
        `).join('');

        resultsContainer.innerHTML = modelsHtml;
    }

    async testSingleModel(model, content, questionType, questionCount, index) {
        const testItem = document.getElementById(`test-item-${index}`);
        if (!testItem) return;

        const statusIcon = testItem.querySelector('.test-status-icon');
        const timing = testItem.querySelector('.test-timing');

        // Update to testing state
        statusIcon.className = 'test-status-icon test-status-testing';
        statusIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        timing.textContent = 'Testing...';

        const startTime = Date.now();

        try {
            // Test the model using dedicated test function
            const result = await window.electronAPI.testModel(
                model.id,
                content,
                questionType,
                questionCount
            );

            const endTime = Date.now();
            const duration = endTime - startTime;

            if (result.success && result.questions && result.questions.length > 0) {
                // Success
                statusIcon.className = 'test-status-icon test-status-success';
                statusIcon.innerHTML = '<i class="fas fa-check"></i>';
                timing.innerHTML = `
                    <span class="test-timing">✓ ${duration}ms</span>
                    <div class="test-success-info">${result.questions.length} questions generated</div>
                `;

                this.testResults.push({
                    model: model,
                    success: true,
                    duration: duration,
                    questionsGenerated: result.questions.length,
                    questions: result.questions
                });
            } else {
                // Failed
                statusIcon.className = 'test-status-icon test-status-failed';
                statusIcon.innerHTML = '<i class="fas fa-times"></i>';
                timing.innerHTML = `
                    <span class="test-timing">✗ ${duration}ms</span>
                    <div class="test-error">${result.error || 'No questions generated'}</div>
                `;

                this.testResults.push({
                    model: model,
                    success: false,
                    duration: duration,
                    error: result.error || 'No questions generated'
                });
            }

        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            // Error
            statusIcon.className = 'test-status-icon test-status-failed';
            statusIcon.innerHTML = '<i class="fas fa-exclamation"></i>';
            timing.innerHTML = `
                <span class="test-timing">✗ ${duration}ms</span>
                <div class="test-error">${error.message}</div>
            `;

            this.testResults.push({
                model: model,
                success: false,
                duration: duration,
                error: error.message
            });
        }
    }

    stopModelTesting() {
        this.testingInProgress = false;
        this.finishModelTesting();
    }

    finishModelTesting() {
        this.testingInProgress = false;

        // Update UI
        const startBtn = document.getElementById('startModelTest');
        const stopBtn = document.getElementById('stopModelTest');
        const exportBtn = document.getElementById('exportTestResults');

        if (startBtn) startBtn.style.display = 'inline-flex';
        if (stopBtn) stopBtn.style.display = 'none';
        if (exportBtn && this.testResults.length > 0) exportBtn.style.display = 'inline-flex';

        // Show summary
        const successCount = this.testResults.filter(r => r.success).length;
        const totalCount = this.testResults.length;

        if (totalCount > 0) {
            this.showNotification(
                `Testing complete: ${successCount}/${totalCount} models working`,
                successCount > 0 ? 'success' : 'warning'
            );
        }
    }

    async clearAllRateLimits() {
        try {
            const result = await window.electronAPI.clearAllRateLimits();

            if (result.success) {
                const clearedCount = result.clearedModels ? result.clearedModels.length : 0;
                this.showNotification(
                    `Cleared rate limits for ${clearedCount} models`,
                    'success'
                );

                // If we have cleared models, show which ones
                if (clearedCount > 0) {
                    console.log('Cleared rate limits for models:', result.clearedModels);
                }
            } else {
                this.showNotification('Failed to clear rate limits: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Error clearing rate limits:', error);
            this.showNotification('Error clearing rate limits: ' + error.message, 'error');
        }
    }

    async exportTestResults() {
        if (!this.testResults || this.testResults.length === 0) {
            this.showNotification('No test results to export', 'warning');
            return;
        }

        try {
            // Create export data
            const exportData = {
                timestamp: new Date().toISOString(),
                testParameters: {
                    content: document.getElementById('testContent').value.trim(),
                    questionType: document.getElementById('testQuestionType').value,
                    questionCount: parseInt(document.getElementById('testQuestionCount').value) || 3
                },
                results: this.testResults.map(result => ({
                    modelId: result.model.id,
                    modelName: result.model.name || result.model.id,
                    success: result.success,
                    duration: result.duration,
                    questionsGenerated: result.questionsGenerated || 0,
                    error: result.error || null
                })),
                summary: {
                    totalModels: this.testResults.length,
                    successfulModels: this.testResults.filter(r => r.success).length,
                    failedModels: this.testResults.filter(r => !r.success).length,
                    averageDuration: Math.round(
                        this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length
                    )
                }
            };

            // Generate filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `model-test-results-${timestamp}.json`;

            // Save file
            const result = await window.electronAPI.saveFile({
                defaultPath: filename,
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ],
                content: JSON.stringify(exportData, null, 2)
            });

            if (result.success) {
                this.showNotification('Test results exported successfully!', 'success');
            } else {
                this.showNotification('Failed to export test results', 'error');
            }

        } catch (error) {
            console.error('Error exporting test results:', error);
            this.showNotification('Error exporting test results: ' + error.message, 'error');
        }
    }





    displayQuestions() {
        this.showScreen('questionsScreen');
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions without answers initially
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, false); // false = hide answers
            questionsDisplay.appendChild(questionElement);
        });

        // Hide export button initially
        document.getElementById('exportQuestionsBtn').style.display = 'none';
    }

    createQuestionElement(question, index, showAnswers = true) {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item';

        let optionsHtml = '';
        if (question.options) {
            // MCQ question
            optionsHtml = question.options.map((option, optIndex) => {
                const letter = String.fromCharCode(65 + optIndex); // A, B, C, D
                const isCorrect = option === question.answer;
                return `
                    <div class="option-item ${showAnswers && isCorrect ? 'correct' : ''}">
                        <div class="option-letter">${letter}</div>
                        <span>${option}</span>
                    </div>
                `;
            }).join('');
        } else {
            // True/False question - use normalized comparison
            const normalizedAnswer = this.normalizeAnswer(question.answer);
            optionsHtml = `
                <div class="option-item ${showAnswers && normalizedAnswer === 'true' ? 'correct' : ''}">
                    <div class="option-letter">T</div>
                    <span>True</span>
                </div>
                <div class="option-item ${showAnswers && normalizedAnswer === 'false' ? 'correct' : ''}">
                    <div class="option-letter">F</div>
                    <span>False</span>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="question-header">
                <div class="question-number">${index + 1}</div>
                <div class="question-text">${question.question}</div>
            </div>
            <div class="question-options">
                ${optionsHtml}
            </div>
            ${showAnswers ? `
                <div class="question-answer">
                    <div class="answer-label">Correct Answer:</div>
                    <div>${this.formatAnswerForDisplay(question.answer, this.selectedQuestionType)}</div>
                </div>
                ${question.explanation ? `
                    <div class="question-explanation">
                        <div class="explanation-label">Explanation:</div>
                        <div>${question.explanation}</div>
                    </div>
                ` : ''}
            ` : ''}
        `;

        return questionDiv;
    }

    showQuestionsWithAnswers() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions with answers and explanations
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, true); // true = show answers
            questionsDisplay.appendChild(questionElement);
        });

        // Show export button when answers are visible
        document.getElementById('exportQuestionsBtn').style.display = 'inline-block';

        // Update the button to "Hide Answers"
        const showAnswersBtn = document.getElementById('showAnswersBtn');
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const hideAnswersText = window.translations[currentLanguage].hideAnswers;
        showAnswersBtn.innerHTML = `<i class="fas fa-eye-slash"></i> ${hideAnswersText}`;

        // Remove existing event listener and add new one
        showAnswersBtn.replaceWith(showAnswersBtn.cloneNode(true));
        const newShowAnswersBtn = document.getElementById('showAnswersBtn');
        newShowAnswersBtn.addEventListener('click', () => {
            this.hideAnswers();
        });
    }

    hideAnswers() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions without answers
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, false); // false = hide answers
            questionsDisplay.appendChild(questionElement);
        });

        // Hide export button when answers are hidden
        document.getElementById('exportQuestionsBtn').style.display = 'none';

        // Update the button back to "Show Questions with Answers"
        const showAnswersBtn = document.getElementById('showAnswersBtn');
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const showAnswersText = window.translations[currentLanguage].showQuestionsWithAnswers;
        showAnswersBtn.innerHTML = `<i class="fas fa-eye"></i> ${showAnswersText}`;

        // Remove existing event listener and add new one
        showAnswersBtn.replaceWith(showAnswersBtn.cloneNode(true));
        const newShowAnswersBtn = document.getElementById('showAnswersBtn');
        newShowAnswersBtn.addEventListener('click', () => {
            this.showQuestionsWithAnswers();
        });
    }

    async exportQuestionsToPDF() {
        try {
            if (!this.currentQuestions || this.currentQuestions.length === 0) {
                this.showNotification('No questions to export', 'warning');
                return;
            }

            // Show loading notification
            this.showNotification('Preparing PDF export...', 'info');

            // Use Electron's save dialog to let user choose location
            const result = await window.electronAPI.saveFile({
                title: 'Save Questions as PDF',
                defaultPath: `questions_${this.selectedQuestionType}_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Generate PDF content
                const pdfContent = this.generatePDFContent();

                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, pdfContent);

                if (saveResult.success) {
                    this.showNotification(`PDF exported successfully to ${result.filePath}`, 'success');
                } else {
                    throw new Error(saveResult.error || 'Failed to save PDF');
                }
            } else if (result.canceled) {
                this.showNotification('Export canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        }
    }

    generatePDFContent() {
        // Generate HTML content for PDF
        let htmlContent = `
            <html>
            <head>
                <title>${this.selectedQuestionType} Questions</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                    .question { margin-bottom: 25px; page-break-inside: avoid; }
                    .question-number { font-weight: bold; color: #333; margin-bottom: 8px; }
                    .question-text { font-size: 16px; margin-bottom: 12px; }
                    .options { margin-left: 20px; margin-bottom: 12px; }
                    .option { margin-bottom: 5px; }
                    .correct-answer { background-color: #d4edda; padding: 8px; border-left: 4px solid #28a745; margin-bottom: 10px; }
                    .explanation { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; font-style: italic; }
                    .answer-label { font-weight: bold; color: #28a745; }
                    .explanation-label { font-weight: bold; color: #007bff; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${this.selectedQuestionType} Questions</h1>
                    <p>Generated on ${new Date().toLocaleDateString('en-US')}</p>
                    <p>Total Questions: ${this.currentQuestions.length}</p>
                </div>
        `;

        this.currentQuestions.forEach((question, index) => {
            htmlContent += `
                <div class="question">
                    <div class="question-number">Question ${index + 1}</div>
                    <div class="question-text">${question.question}</div>
                    <div class="options">
            `;

            if (question.options) {
                // MCQ question
                question.options.forEach((option, optIndex) => {
                    const letter = String.fromCharCode(65 + optIndex);
                    htmlContent += `<div class="option">${letter}. ${option}</div>`;
                });
            } else {
                // True/False question
                htmlContent += `
                    <div class="option">T. True</div>
                    <div class="option">F. False</div>
                `;
            }

            htmlContent += `
                    </div>
                    <div class="correct-answer">
                        <span class="answer-label">Correct Answer:</span>
                        ${this.formatAnswerForDisplay(question.answer, this.selectedQuestionType)}
                    </div>
            `;

            if (question.explanation) {
                htmlContent += `
                    <div class="explanation">
                        <span class="explanation-label">Explanation:</span>
                        ${question.explanation}
                    </div>
                `;
            }

            htmlContent += `</div>`;
        });

        htmlContent += `
            </body>
            </html>
        `;

        return htmlContent;
    }

    // History and Statistics Methods
    async showHistoryScreen() {
        console.log('Showing history screen...');
        this.showScreen('historyScreen');
        await this.loadHistory();
        await this.loadSavedQuizzes();
    }

    async showStatisticsScreen() {
        console.log('Showing statistics screen...');
        this.showScreen('statisticsScreen');
        await this.loadStatistics();
    }











    async loadHistory() {
        try {
            // Reset pagination when loading
            this.historyPage = 1;

            const historyList = document.getElementById('historyList');
            // Get current language for translations
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const loadingText = isArabic ? 'تحميل تاريخ الاختبارات...' : 'Loading quiz history...';

            historyList.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i><p>${loadingText}</p></div>`;

            const result = await window.electronAPI.getQuizHistory();

            if (result.success && result.sessions && result.sessions.length > 0) {
                this.displayHistory(result.sessions);
                this.updateHistorySummary(result.sessions);
            } else {
                // Show empty state when no history exists
                this.showEmptyHistory();
                this.updateHistorySummary([]); // Update summary with empty data
            }
        } catch (error) {
            console.error('Error loading history:', error);
            // Show empty state on error
            this.showEmptyHistory();
            this.updateHistorySummary([]);
        }
    }

    async loadSavedQuizzes() {
        try {
            // Reset pagination when loading
            this.savedQuizzesPage = 1;

            const savedQuizzesList = document.getElementById('savedQuizzesList');
            // Get current language for translations
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const loadingText = isArabic ? 'تحميل الاختبارات المحفوظة...' : 'Loading saved quizzes...';

            savedQuizzesList.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i><p>${loadingText}</p></div>`;

            // Automatically fix Islamic dates in existing quiz titles (silently)
            await this.autoFixIslamicDates();

            const result = await window.electronAPI.getSavedQuizzes();
            console.log('Get saved quizzes result:', result);

            if (result.success && result.savedQuizzes && result.savedQuizzes.length > 0) {
                console.log(`Displaying ${result.savedQuizzes.length} saved quizzes`);
                this.displaySavedQuizzes(result.savedQuizzes);
            } else {
                console.log('No saved quizzes found, showing empty state');
                this.showEmptySavedQuizzes();
            }
        } catch (error) {
            console.error('Error loading saved quizzes:', error);
            this.showEmptySavedQuizzes();
        }
    }

    displaySavedQuizzes(savedQuizzes) {
        const savedQuizzesList = document.getElementById('savedQuizzesList');

        if (!savedQuizzes || savedQuizzes.length === 0) {
            this.showEmptySavedQuizzes();
            document.getElementById('savedQuizzesPagination').style.display = 'none';
            return;
        }

        // Store all quizzes for pagination
        this.allSavedQuizzes = savedQuizzes;

        // Calculate pagination
        const startIndex = (this.savedQuizzesPage - 1) * this.savedQuizzesPerPage;
        const endIndex = startIndex + this.savedQuizzesPerPage;
        const paginatedQuizzes = savedQuizzes.slice(startIndex, endIndex);

        // Clear and populate list
        savedQuizzesList.innerHTML = '';

        paginatedQuizzes.forEach(quiz => {
            const quizItem = this.createSavedQuizItem(quiz);
            savedQuizzesList.appendChild(quizItem);
        });

        // Update pagination controls
        this.updateSavedQuizzesPagination();
    }

    createSavedQuizItem(quiz) {
        const item = document.createElement('div');
        item.className = 'saved-quiz-item';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Fix date parsing and formatting
        let date;
        try {
            date = new Date(quiz.created_at || quiz.createdAt);
            // If date is invalid, use current date
            if (isNaN(date.getTime())) {
                date = new Date();
            }
        } catch (error) {
            console.warn('Error parsing quiz date:', error);
            date = new Date();
        }

        const questionCount = quiz.questions ? quiz.questions.length : 0;

        // Get the question type from either field name (handle both camelCase and snake_case)
        const questionType = quiz.questionType || quiz.question_type || 'Quiz';

        item.innerHTML = `
            <div class="saved-quiz-header">
                <div class="saved-quiz-title">${quiz.title}</div>
                <div class="saved-quiz-type">${questionType}</div>
            </div>
            <div class="saved-quiz-info">
                <div class="saved-quiz-date">
                    <i class="fas fa-calendar"></i>
                    ${date.toLocaleDateString('en-US')}
                </div>
                <div class="saved-quiz-questions">
                    <i class="fas fa-question-circle"></i>
                    ${questionCount} ${isArabic ? 'سؤال' : 'questions'}
                </div>
            </div>
            <div class="saved-quiz-actions">
                <button class="btn btn-primary btn-sm take-quiz-btn" data-quiz-id="${quiz.id}">
                    <i class="fas fa-play"></i> ${isArabic ? 'خذ الاختبار' : 'Take Quiz'}
                </button>
                <button class="btn btn-danger btn-sm delete-quiz-btn" data-quiz-id="${quiz.id}">
                    <i class="fas fa-trash"></i> ${isArabic ? 'حذف' : 'Delete'}
                </button>
            </div>
        `;

        // Add event listeners
        const takeQuizBtn = item.querySelector('.take-quiz-btn');
        const deleteQuizBtn = item.querySelector('.delete-quiz-btn');

        takeQuizBtn.addEventListener('click', () => this.takeSavedQuiz(quiz));
        deleteQuizBtn.addEventListener('click', () => this.deleteSavedQuiz(quiz.id));

        return item;
    }

    showEmptySavedQuizzes() {
        const savedQuizzesList = document.getElementById('savedQuizzesList');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        savedQuizzesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-bookmark"></i>
                <h3>${isArabic ? 'لا توجد اختبارات محفوظة' : 'No Saved Quizzes'}</h3>
                <p>${isArabic ? 'احفظ اختباراً من صفحة النتائج لرؤيته هنا!' : 'Save a quiz from the results page to see it here!'}</p>
            </div>
        `;
    }

    async clearAllSavedQuizzes() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد حذف جميع الاختبارات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.'
            : 'Are you sure you want to delete all saved quizzes? This action cannot be undone.';

        if (confirm(confirmMessage)) {
            try {
                const result = await window.electronAPI.clearAllSavedQuizzes();

                if (result.success) {
                    const successMessage = isArabic ? 'تم حذف جميع الاختبارات المحفوظة بنجاح' : 'All saved quizzes deleted successfully';
                    this.showNotification(successMessage, 'success');
                    await this.loadSavedQuizzes(); // Reload the list
                } else {
                    const errorMessage = isArabic ? 'فشل في حذف الاختبارات المحفوظة' : 'Failed to delete saved quizzes';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error clearing all saved quizzes:', error);
                const errorMessage = isArabic ? 'حدث خطأ أثناء حذف الاختبارات المحفوظة' : 'An error occurred while deleting saved quizzes';
                this.showNotification(errorMessage, 'error');
            }
        }
    }

    async autoFixIslamicDates() {
        // Silently fix Islamic dates without showing notifications
        try {
            const result = await window.electronAPI.fixIslamicDates();

            if (result.success && result.updatedCount > 0) {
                console.log(`Automatically fixed ${result.updatedCount} Islamic dates in quiz titles`);
            }
        } catch (error) {
            console.warn('Error auto-fixing Islamic dates:', error);
            // Don't show error notifications for automatic fixes
        }
    }

    async takeSavedQuiz(quiz) {
        try {
            // Load the saved quiz questions
            this.currentQuestions = quiz.questions;
            this.selectedQuestionType = quiz.questionType || quiz.question_type || 'MCQ';
            this.currentFileName = quiz.source;

            // Get current language for user-friendly messages
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            const message = isArabic ?
                `تم تحميل الاختبار: ${quiz.title}` :
                `Loaded quiz: ${quiz.title}`;
            this.showNotification(message, 'success');

            // Start the quiz
            this.startInteractiveQuiz();
        } catch (error) {
            console.error('Error taking saved quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                'فشل في تحميل الاختبار المحفوظ.' :
                'Failed to load saved quiz.';
            this.showNotification(message, 'error');
        }
    }

    async deleteSavedQuiz(quizId) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد حذف هذا الاختبار المحفوظ؟'
            : 'Are you sure you want to delete this saved quiz?';

        if (confirm(confirmMessage)) {
            try {
                // TODO: Implement delete saved quiz API call
                const result = await window.electronAPI.deleteSavedQuiz(quizId);

                if (result.success) {
                    const successMessage = isArabic ? 'تم حذف الاختبار بنجاح' : 'Quiz deleted successfully';
                    this.showNotification(successMessage, 'success');
                    await this.loadSavedQuizzes(); // Reload the list
                } else {
                    const errorMessage = isArabic ? 'فشل في حذف الاختبار' : 'Failed to delete quiz';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error deleting saved quiz:', error);
                const errorMessage = isArabic ? 'حدث خطأ أثناء حذف الاختبار' : 'An error occurred while deleting the quiz';
                this.showNotification(errorMessage, 'error');
            }
        }
    }

    displaySampleHistory() {
        const sampleSessions = [
            {
                id: 1,
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                question_type: 'MCQ',
                score_correct: 8,
                score_total: 10,
                duration: 180,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 2,
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                question_type: 'TF',
                score_correct: 7,
                score_total: 10,
                duration: 120,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 3,
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                question_type: 'MCQ',
                score_correct: 9,
                score_total: 12,
                duration: 240,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 4,
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
                question_type: 'TF',
                score_correct: 15,
                score_total: 15,
                duration: 300,
                answers: '[]',
                questions: '[]'
            }
        ];

        this.displayHistory(sampleSessions);
        this.updateHistorySummary(sampleSessions);
    }

    displayHistory(sessions) {
        const historyList = document.getElementById('historyList');

        if (!sessions || sessions.length === 0) {
            this.showEmptyHistory();
            document.getElementById('historyPagination').style.display = 'none';
            return;
        }

        // Store filtered sessions for pagination (don't overwrite allHistorySessions if this is filtered data)
        const isFiltered = this.isHistoryFiltered();
        if (!isFiltered) {
            this.allHistorySessions = sessions;
        }

        // Calculate pagination
        const startIndex = (this.historyPage - 1) * this.historyPerPage;
        const endIndex = startIndex + this.historyPerPage;
        const paginatedSessions = sessions.slice(startIndex, endIndex);

        // Clear and populate list
        historyList.innerHTML = '';

        paginatedSessions.forEach(session => {
            const historyItem = this.createHistoryItem(session);
            historyList.appendChild(historyItem);
        });

        // Update pagination controls with current session data
        this.updateHistoryPagination(sessions);
    }

    isHistoryFiltered() {
        const typeFilter = document.getElementById('historyTypeFilter')?.value;
        const dateFilter = document.getElementById('historyDateFilter')?.value;
        return (typeFilter && typeFilter !== 'all') || (dateFilter && dateFilter !== 'all');
    }

    createHistoryItem(session) {
        const item = document.createElement('div');
        item.className = 'history-item';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Fix date parsing for session timestamp
        let date;
        try {
            date = new Date(session.timestamp);
            if (isNaN(date.getTime())) {
                date = new Date(); // Use current date if invalid
            }
        } catch (error) {
            console.warn('Error parsing session timestamp:', error);
            date = new Date();
        }
        // Calculate percentage with null safety and NaN protection
        let scorePercentage = 0;
        if (session.score_total > 0 && session.score_correct >= 0) {
            scorePercentage = Math.round((session.score_correct / session.score_total) * 100);
            if (isNaN(scorePercentage)) {
                scorePercentage = 0;
            }
        }
        const scoreClass = this.getScoreClass(scorePercentage);
        const duration = this.formatDuration(session.duration);

        // Get translated question type - handle both string and object cases
        let questionType = session.question_type;
        if (typeof questionType === 'object') {
            questionType = 'MCQ'; // Default fallback
        }

        const questionTypeText = questionType === 'MCQ'
            ? (isArabic ? 'متعدد الخيارات' : 'Multiple Choice')
            : (isArabic ? 'صح أو خطأ' : 'True/False');

        item.innerHTML = `
            <div class="history-item-header">
                <div class="history-item-title">
                    <i class="fas fa-${questionType === 'MCQ' ? 'list' : 'check'}"></i>
                    ${questionTypeText} ${isArabic ? 'اختبار' : 'Quiz'}
                </div>
                <div class="history-item-date">
                    ${date.toLocaleDateString('en-US')} ${date.toLocaleTimeString('en-US', { hour12: false })}
                </div>
            </div>
            <div class="history-item-stats">
                <div class="history-stat">
                    <div class="history-stat-value ${scoreClass}">${scorePercentage}%</div>
                    <div class="history-stat-label">${isArabic ? 'النتيجة' : 'Score'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${session.score_correct}/${session.score_total}</div>
                    <div class="history-stat-label">${isArabic ? 'صحيح' : 'Correct'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${duration}</div>
                    <div class="history-stat-label">${isArabic ? 'المدة' : 'Duration'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${session.score_total}</div>
                    <div class="history-stat-label">${isArabic ? 'الأسئلة' : 'Questions'}</div>
                </div>
            </div>
        `;

        return item;
    }

    getScoreClass(percentage) {
        if (percentage >= 90) return 'score-excellent';
        if (percentage >= 75) return 'score-good';
        if (percentage >= 60) return 'score-average';
        return 'score-poor';
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        if (minutes > 0) {
            return `${minutes}m ${remainingSeconds}s`;
        }
        return `${remainingSeconds}s`;
    }

    updateHistorySummary(sessions) {
        if (!sessions || sessions.length === 0) {
            document.getElementById('totalQuizzesHistory').textContent = '0';
            document.getElementById('avgScoreHistory').textContent = '0%';
            document.getElementById('avgTimeHistory').textContent = '0m';
            return;
        }

        const totalQuizzes = sessions.length;
        const totalScore = sessions.reduce((sum, session) =>
            sum + (session.score_correct / session.score_total), 0);
        const avgScore = Math.round((totalScore / totalQuizzes) * 100);
        const totalTime = sessions.reduce((sum, session) => sum + session.duration, 0);
        const avgTime = Math.round(totalTime / totalQuizzes);

        document.getElementById('totalQuizzesHistory').textContent = totalQuizzes;
        document.getElementById('avgScoreHistory').textContent = `${avgScore}%`;
        document.getElementById('avgTimeHistory').textContent = this.formatDuration(avgTime);
    }

    showEmptyHistory() {
        const historyList = document.getElementById('historyList');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        historyList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h3>${isArabic ? 'لا يوجد تاريخ اختبارات' : 'No Quiz History'}</h3>
                <p>${isArabic ? 'لم تقم بأي اختبارات بعد.<br>ابدأ اختباراً لرؤية تاريخك هنا!' : 'You haven\'t taken any quizzes yet.<br>Start a quiz to see your history here!'}</p>
            </div>
        `;
    }

    async loadStatistics() {
        try {
            // Load quiz history to calculate statistics from actual data
            const historyResult = await window.electronAPI.getQuizHistory();

            if (historyResult.success && historyResult.sessions && historyResult.sessions.length > 0) {
                // Calculate statistics from quiz history
                this.calculateStatisticsFromHistory(historyResult.sessions);
                this.calculateDetailedStats(historyResult.sessions);
            } else {
                // Show empty statistics when no quiz history exists
                this.showEmptyStatistics();
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
            // Show empty statistics on error
            this.showEmptyStatistics();
        }
    }

    displaySampleStatistics() {
        const sampleStats = {
            totalQuizzes: 4,
            averageScore: 82,
            totalQuestions: 47,
            lastQuizDate: new Date().toISOString()
        };

        this.displayStatistics(sampleStats);

        // Use the same sample sessions for detailed stats
        const sampleSessions = [
            {
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                question_type: 'MCQ',
                score_correct: 8,
                score_total: 10,
                duration: 180
            },
            {
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'TF',
                score_correct: 7,
                score_total: 10,
                duration: 120
            },
            {
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'MCQ',
                score_correct: 9,
                score_total: 12,
                duration: 240
            },
            {
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'TF',
                score_correct: 15,
                score_total: 15,
                duration: 300
            }
        ];

        this.calculateDetailedStats(sampleSessions);
    }

    async loadDetailedStats() {
        try {
            const historyResult = await window.electronAPI.getQuizHistory();

            if (historyResult.success && historyResult.sessions) {
                this.calculateDetailedStats(historyResult.sessions);
            }
        } catch (error) {
            console.error('Error loading detailed stats:', error);
        }
    }

    calculateStatisticsFromHistory(sessions) {
        if (!sessions || sessions.length === 0) {
            this.showEmptyStatistics();
            return;
        }

        // Calculate overall statistics
        let totalCorrect = 0;
        let totalQuestions = 0;
        let totalQuizzes = sessions.length;

        sessions.forEach(session => {
            totalCorrect += session.score_correct || 0;
            totalQuestions += session.score_total || 0;
        });

        // Calculate average score
        const averageScore = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;

        // Update overview stats
        document.getElementById('overallScore').textContent = `${averageScore}%`;
        document.getElementById('currentStreak').textContent = '0'; // TODO: Implement streak calculation

        // Update basic stats
        document.getElementById('totalQuestions').textContent = totalQuestions;
        document.getElementById('correctAnswers').textContent = totalCorrect;
        document.getElementById('incorrectAnswers').textContent = totalQuestions - totalCorrect;
    }

    displayStatistics(stats) {
        // Update overview stats with NaN protection
        const averageScore = isNaN(stats.averageScore) || stats.averageScore === null ? 0 : stats.averageScore;
        document.getElementById('overallScore').textContent = `${averageScore}%`;
        document.getElementById('currentStreak').textContent = '0'; // TODO: Implement streak calculation

        // Update basic stats with null safety
        document.getElementById('totalQuestions').textContent = stats.totalQuestions || 0;
        document.getElementById('correctAnswers').textContent = '0'; // Will be calculated from detailed stats
        document.getElementById('incorrectAnswers').textContent = '0'; // Will be calculated from detailed stats
    }

    calculateDetailedStats(sessions) {
        if (!sessions || sessions.length === 0) {
            this.showEmptyStatistics();
            return;
        }

        // Calculate totals
        let totalCorrect = 0;
        let totalQuestions = 0;
        let mcqCount = 0;
        let tfCount = 0;
        let todayCount = 0;
        let weekCount = 0;
        let monthCount = 0;

        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        sessions.forEach(session => {
            totalCorrect += session.score_correct;
            totalQuestions += session.score_total;

            if (session.question_type === 'MCQ') mcqCount++;
            else tfCount++;

            // Fix date parsing for session timestamp
            let sessionDate;
            try {
                sessionDate = new Date(session.timestamp);
                if (isNaN(sessionDate.getTime())) {
                    sessionDate = new Date(); // Use current date if invalid
                }
            } catch (error) {
                sessionDate = new Date();
            }
            if (sessionDate.toDateString() === today.toDateString()) todayCount++;
            if (sessionDate >= weekAgo) weekCount++;
            if (sessionDate >= monthAgo) monthCount++;
        });

        const totalIncorrect = totalQuestions - totalCorrect;

        // Update performance metrics
        document.getElementById('totalQuestions').textContent = totalQuestions;
        document.getElementById('correctAnswers').textContent = totalCorrect;
        document.getElementById('incorrectAnswers').textContent = totalIncorrect;

        // Update question types with progress bars
        const totalQuizzes = sessions.length;
        const mcqPercentage = totalQuizzes > 0 ? Math.round((mcqCount / totalQuizzes) * 100) : 0;
        const tfPercentage = totalQuizzes > 0 ? Math.round((tfCount / totalQuizzes) * 100) : 0;

        document.getElementById('mcqQuizzes').textContent = mcqCount;
        document.getElementById('tfQuizzes').textContent = tfCount;
        document.getElementById('mcqPercentage').textContent = `${mcqPercentage}%`;
        document.getElementById('tfPercentage').textContent = `${tfPercentage}%`;

        // Animate progress bars
        setTimeout(() => {
            document.getElementById('mcqProgress').style.width = `${mcqPercentage}%`;
            document.getElementById('tfProgress').style.width = `${tfPercentage}%`;
        }, 500);

        // Update activity stats
        document.getElementById('quizzesToday').textContent = todayCount;
        document.getElementById('quizzesThisWeek').textContent = weekCount;
        document.getElementById('quizzesThisMonth').textContent = monthCount;
    }



    showEmptyStatistics() {
        // Show empty state for statistics
        const statsContent = document.querySelector('.statistics-content');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        statsContent.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-chart-bar"></i>
                <h3>${isArabic ? 'لا توجد إحصائيات متاحة' : 'No Statistics Available'}</h3>
                <p>${isArabic ? 'قم ببعض الاختبارات لرؤية إحصائياتك وإنجازاتك!' : 'Take some quizzes to see your statistics and achievements!'}</p>
            </div>
        `;
    }

    async clearHistory() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد مسح كل تاريخ الاختبارات؟ لا يمكن التراجع عن هذا الإجراء.'
            : 'Are you sure you want to clear all quiz history? This action cannot be undone.';

        if (confirm(confirmMessage)) {
            try {
                // Call the backend API to clear history
                const result = await window.electronAPI.clearQuizHistory();

                if (result.success) {
                    const successMessage = isArabic ? 'تم مسح التاريخ بنجاح' : 'History cleared successfully';
                    this.showNotification(successMessage, 'success');

                    // Reload the history page to show empty state
                    await this.loadHistory();

                    // Also refresh statistics since they depend on history data
                    if (this.currentScreen === 'statisticsScreen') {
                        await this.loadStatistics();
                    }
                } else {
                    const errorMessage = isArabic ? 'فشل في مسح التاريخ' : 'Failed to clear history';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error clearing history:', error);
                const errorMessage = isArabic ? 'فشل في مسح التاريخ' : 'Failed to clear history';
                this.showNotification(errorMessage, 'error');
            }
        }
    }



    filterHistory() {
        if (!this.allHistorySessions || this.allHistorySessions.length === 0) {
            return;
        }

        const typeFilter = document.getElementById('historyTypeFilter').value;
        const dateFilter = document.getElementById('historyDateFilter').value;

        let filteredSessions = [...this.allHistorySessions];

        // Filter by question type
        if (typeFilter !== 'all') {
            filteredSessions = filteredSessions.filter(session => {
                let questionType = session.question_type;
                if (typeof questionType === 'object') {
                    questionType = 'MCQ'; // Default fallback
                }
                return questionType === typeFilter;
            });
        }

        // Filter by date
        if (dateFilter !== 'all') {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            filteredSessions = filteredSessions.filter(session => {
                let sessionDate;
                try {
                    sessionDate = new Date(session.timestamp);
                    if (isNaN(sessionDate.getTime())) {
                        return false; // Invalid date
                    }
                } catch (error) {
                    return false; // Invalid date
                }

                switch (dateFilter) {
                    case 'today':
                        return sessionDate >= today;
                    case 'yesterday':
                        return sessionDate >= yesterday && sessionDate < today;
                    case 'week':
                        return sessionDate >= weekAgo;
                    case 'month':
                        return sessionDate >= monthAgo;
                    default:
                        return true;
                }
            });
        }

        // Reset pagination and display filtered results
        this.historyPage = 1;
        this.displayHistory(filteredSessions);
    }

    // Pagination methods
    changeSavedQuizzesPage(direction) {
        const newPage = this.savedQuizzesPage + direction;
        const totalPages = Math.ceil(this.allSavedQuizzes.length / this.savedQuizzesPerPage);

        if (newPage >= 1 && newPage <= totalPages) {
            this.savedQuizzesPage = newPage;
            this.displaySavedQuizzes(this.allSavedQuizzes);
        }
    }

    changeHistoryPage(direction) {
        // Check if we're currently filtering
        if (this.isHistoryFiltered()) {
            // Re-apply filters to get current filtered data
            this.historyPage += direction;
            this.filterHistory();
        } else {
            const newPage = this.historyPage + direction;
            const totalPages = Math.ceil(this.allHistorySessions.length / this.historyPerPage);

            if (newPage >= 1 && newPage <= totalPages) {
                this.historyPage = newPage;
                this.displayHistory(this.allHistorySessions);
            }
        }
    }

    updateSavedQuizzesPagination() {
        const totalPages = Math.ceil(this.allSavedQuizzes.length / this.savedQuizzesPerPage);
        const paginationContainer = document.getElementById('savedQuizzesPagination');
        const pageInfo = document.getElementById('savedQuizzesPageInfo');
        const prevBtn = document.getElementById('savedQuizzesPrevBtn');
        const nextBtn = document.getElementById('savedQuizzesNextBtn');
        const pageNumbers = document.getElementById('savedQuizzesPageNumbers');

        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        pageInfo.textContent = isArabic
            ? `صفحة ${this.savedQuizzesPage} من ${totalPages}`
            : `Page ${this.savedQuizzesPage} of ${totalPages}`;

        prevBtn.disabled = this.savedQuizzesPage === 1;
        nextBtn.disabled = this.savedQuizzesPage === totalPages;

        // Generate page numbers
        pageNumbers.innerHTML = '';
        const startPage = Math.max(1, this.savedQuizzesPage - 2);
        const endPage = Math.min(totalPages, this.savedQuizzesPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `page-number ${i === this.savedQuizzesPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.savedQuizzesPage = i;
                this.displaySavedQuizzes(this.allSavedQuizzes);
            });
            pageNumbers.appendChild(pageBtn);
        }
    }

    updateHistoryPagination(sessions = null) {
        const sessionsToUse = sessions || this.allHistorySessions;
        const totalPages = Math.ceil(sessionsToUse.length / this.historyPerPage);
        const paginationContainer = document.getElementById('historyPagination');
        const pageInfo = document.getElementById('historyPageInfo');
        const prevBtn = document.getElementById('historyPrevBtn');
        const nextBtn = document.getElementById('historyNextBtn');
        const pageNumbers = document.getElementById('historyPageNumbers');

        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        pageInfo.textContent = isArabic
            ? `صفحة ${this.historyPage} من ${totalPages}`
            : `Page ${this.historyPage} of ${totalPages}`;

        prevBtn.disabled = this.historyPage === 1;
        nextBtn.disabled = this.historyPage === totalPages;

        // Generate page numbers
        pageNumbers.innerHTML = '';
        const startPage = Math.max(1, this.historyPage - 2);
        const endPage = Math.min(totalPages, this.historyPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `page-number ${i === this.historyPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.historyPage = i;
                // Re-apply filters when changing pages
                if (this.isHistoryFiltered()) {
                    this.filterHistory();
                } else {
                    this.displayHistory(this.allHistorySessions);
                }
            });
            pageNumbers.appendChild(pageBtn);
        }
    }

    async exportStatistics() {
        try {
            // Generate statistics content
            const statsContent = this.generateStatisticsContent();

            // Use Electron's save dialog to let user choose location
            const result = await window.electronAPI.saveFile({
                title: 'Save Statistics as PDF',
                defaultPath: `statistics_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, statsContent);

                if (saveResult.success) {
                    this.showNotification('Statistics exported successfully!', 'success');
                } else {
                    this.showNotification('Failed to export statistics', 'error');
                }
            }
        } catch (error) {
            console.error('Error exporting statistics:', error);
            this.showNotification('Failed to export statistics', 'error');
        }
    }

    generateStatisticsContent() {
        // Generate HTML content for statistics PDF
        return `
            <html>
            <head>
                <title>Quiz Statistics</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .stat-section { margin-bottom: 20px; }
                    .stat-item { margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Quiz Statistics Report</h1>
                    <p>Generated on ${new Date().toLocaleDateString('en-US')}</p>
                </div>
                <div class="stat-section">
                    <h2>Performance Overview</h2>
                    <div class="stat-item">Overall Score: ${document.getElementById('overallScore')?.textContent || 'N/A'}</div>
                    <div class="stat-item">Total Questions: ${document.getElementById('totalQuestions')?.textContent || 'N/A'}</div>
                    <div class="stat-item">Correct Answers: ${document.getElementById('correctAnswers')?.textContent || 'N/A'}</div>
                </div>
            </body>
            </html>
        `;
    }

    startInteractiveQuiz() {
        if (!this.currentQuestions || this.currentQuestions.length === 0) {
            this.showNotification('No questions available for quiz', 'warning');
            return;
        }

        // Initialize quiz state
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: this.currentQuestions.length },
            startTime: new Date(),
            endTime: null
        };

        this.showScreen('quizScreen');
        this.displayQuizQuestion();
    }

    displayQuizQuestion() {
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];
        const questionNumber = this.quizState.currentQuestionIndex + 1;
        const totalQuestions = this.currentQuestions.length;

        console.log(`Displaying question ${questionNumber} of ${totalQuestions}`);
        console.log('Question:', currentQuestion.question);

        // Update header
        document.getElementById('questionNumber').textContent = `Question ${questionNumber}`;
        document.getElementById('questionCount').textContent = `of ${totalQuestions}`;
        document.getElementById('currentScore').textContent =
            `${this.quizState.score.correct}/${this.quizState.currentQuestionIndex}`;

        // Display question
        document.getElementById('quizQuestion').textContent = currentQuestion.question;

        // Display options
        const optionsContainer = document.getElementById('quizOptions');
        optionsContainer.innerHTML = '';

        if (currentQuestion.options) {
            // MCQ question
            currentQuestion.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                const letter = String.fromCharCode(65 + index); // A, B, C, D
                optionElement.dataset.answer = letter; // Store the letter, not the option text

                optionElement.innerHTML = `
                    <div class="option-indicator">${letter}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        } else {
            // True/False question
            ['True', 'False'].forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                optionElement.dataset.answer = option;

                optionElement.innerHTML = `
                    <div class="option-indicator">${option[0]}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        }

        // Reset UI state
        this.resetQuizButtonStates();

        // Re-enable option selection for new question
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'auto';
            option.classList.remove('selected', 'correct', 'incorrect');
        });
    }

    resetQuizButtonStates() {
        console.log('Resetting quiz button states');

        const submitBtn = document.getElementById('submitAnswer');
        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');
        const feedbackElement = document.getElementById('quizFeedback');

        // Reset submit button
        submitBtn.disabled = true;
        submitBtn.classList.remove('hidden');

        // Hide other buttons
        nextBtn.classList.add('hidden');
        finishBtn.classList.add('hidden');

        // Hide feedback
        feedbackElement.classList.add('hidden');

        console.log('Quiz button states reset successfully');
    }

    selectQuizOption(selectedElement) {
        console.log('selectQuizOption called with:', selectedElement);

        // Remove previous selection
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Select current option
        selectedElement.classList.add('selected');

        // Enable submit button
        const submitBtn = document.getElementById('submitAnswer');
        submitBtn.disabled = false;
        submitBtn.classList.remove('hidden'); // Ensure button is visible

        console.log('Submit button enabled and made visible');
    }

    submitQuizAnswer() {
        console.log('submitQuizAnswer called');

        // Check if button is disabled
        const submitBtn = document.getElementById('submitAnswer');
        if (submitBtn.disabled) {
            console.log('Submit button is disabled, ignoring click');
            return;
        }

        // Add visual feedback to show button was clicked
        submitBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            submitBtn.style.transform = 'scale(1)';
        }, 100);

        const selectedOption = document.querySelector('.quiz-option.selected');
        if (!selectedOption) {
            console.log('No option selected');
            // Get current language for user-friendly message
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ? 'يرجى اختيار إجابة أولاً' : 'Please select an answer first';
            this.showNotification(message, 'warning');
            return;
        }

        const userAnswer = selectedOption.dataset.answer;
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];

        // For MCQ questions, compare letters directly; for TF, use normalized comparison
        let isCorrect;
        if (currentQuestion.options) {
            // MCQ: Direct letter comparison (A, B, C, D)
            isCorrect = userAnswer === currentQuestion.answer;
        } else {
            // TF: Use normalized comparison for True/False
            const normalizedUserAnswer = this.normalizeAnswer(userAnswer);
            const normalizedCorrectAnswer = this.normalizeAnswer(currentQuestion.answer);
            isCorrect = normalizedUserAnswer === normalizedCorrectAnswer;
        }

        console.log(`Question ${this.quizState.currentQuestionIndex + 1}:`);
        console.log(`  Question text: "${currentQuestion.question}"`);
        console.log(`  User answered: "${userAnswer}"`);
        console.log(`  Correct answer: "${currentQuestion.answer}"`);
        console.log(`  Question type: ${currentQuestion.options ? 'MCQ' : 'TF'}`);
        console.log(`  Explanation: "${currentQuestion.explanation || 'No explanation'}"`);
        console.log(`  Is correct: ${isCorrect}`);

        // Additional validation for True/False questions
        if (this.selectedQuestionType === 'TF') {
            console.log(`  TF Question Analysis:`);
            console.log(`    - Question asks if statement is true`);
            console.log(`    - AI says correct answer is: ${currentQuestion.answer}`);
            console.log(`    - User selected: ${userAnswer}`);
            console.log(`    - Match result: ${isCorrect}`);

            // Check for potential logic errors
            if (currentQuestion.explanation) {
                const explanation = currentQuestion.explanation.toLowerCase();
                if ((explanation.includes('unknown') || explanation.includes('no specific') || explanation.includes('not identifiable')) &&
                    normalizedCorrectAnswer === 'true') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests FALSE but answer is TRUE`);
                }
                if ((explanation.includes('specific') || explanation.includes('identifiable') || explanation.includes('known cause')) &&
                    normalizedCorrectAnswer === 'false') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests TRUE but answer is FALSE`);
                }
            }
        }

        // Record answer
        this.quizState.answers.push({
            questionIndex: this.quizState.currentQuestionIndex,
            userAnswer: userAnswer,
            correctAnswer: currentQuestion.answer,
            isCorrect: isCorrect,
            question: currentQuestion.question
        });

        if (isCorrect) {
            this.quizState.score.correct++;
        }

        // Show feedback
        this.showQuizFeedback(isCorrect, currentQuestion, userAnswer);

        // Update UI - Hide submit button and show appropriate next button
        submitBtn.classList.add('hidden');
        submitBtn.disabled = true; // Disable to prevent double submission

        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');

        const isLastQuestion = this.quizState.currentQuestionIndex >= this.currentQuestions.length - 1;
        console.log(`Current question index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}, Is last question: ${isLastQuestion}`);

        if (isLastQuestion) {
            nextBtn.classList.add('hidden');
            finishBtn.classList.remove('hidden');
            console.log('Showing finish button');
        } else {
            finishBtn.classList.add('hidden');
            nextBtn.classList.remove('hidden');
            console.log('Showing next button');
        }

        // Disable option selection and highlight correct/incorrect answers
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'none';

            // For MCQ questions, compare letters directly; for TF, use normalized comparison
            const optionAnswer = option.dataset.answer;
            const correctAnswer = currentQuestion.answer;

            let isCorrectOption;
            if (currentQuestion.options) {
                // MCQ: Direct letter comparison (A, B, C, D)
                isCorrectOption = optionAnswer === correctAnswer;
            } else {
                // TF: Use normalized comparison
                isCorrectOption = this.normalizeAnswer(optionAnswer) === this.normalizeAnswer(correctAnswer);
            }

            if (isCorrectOption) {
                option.classList.add('correct');
            } else if (option.classList.contains('selected') && !isCorrect) {
                option.classList.add('incorrect');
            }
        });
    }

    normalizeAnswer(answer) {
        if (!answer && answer !== 0 && answer !== false) return '';

        // Handle boolean values
        if (answer === true || answer === false) {
            return answer ? 'true' : 'false';
        }

        // Convert to string and normalize
        const normalized = String(answer).toLowerCase().trim();

        // Handle common variations - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'true';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'false';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            return 'true';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            return 'false';
        }

        // For MCQ, return as-is but normalized
        return normalized;
    }

    formatAnswerForDisplay(answer, questionType = null) {
        // Handle boolean values FIRST (including false)
        if (answer === true || answer === false) {
            console.log('formatAnswerForDisplay: Boolean value detected:', answer);
            return answer ? 'True' : 'False';
        }

        // Handle null/undefined (but not false!)
        if (answer === null || answer === undefined) {
            console.warn('formatAnswerForDisplay: Null/undefined answer received:', answer);
            return questionType === 'TF' ? 'True' : 'Unknown'; // Default only for null/undefined
        }

        const normalized = String(answer).toLowerCase().trim();
        console.log('formatAnswerForDisplay: Processing answer:', answer, 'normalized:', normalized);

        // Format True/False answers nicely - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'True';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'False';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            console.log('formatAnswerForDisplay: Found True keyword in:', normalized);
            return 'True';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            console.log('formatAnswerForDisplay: Found False keyword in:', normalized);
            return 'False';
        }

        // For True/False questions, if we can't determine the answer, log error and return as-is
        if (questionType === 'TF') {
            console.error('formatAnswerForDisplay: Could not determine True/False for answer:', answer, 'type:', typeof answer);
            // Don't default to True - return the original value to debug the issue
            return String(answer);
        }

        // For MCQ answers, return the original answer
        return String(answer);
    }

    showQuizFeedback(isCorrect, question, userAnswer) {
        const feedbackElement = document.getElementById('quizFeedback');
        feedbackElement.className = `quiz-feedback ${isCorrect ? 'feedback-correct' : 'feedback-incorrect'}`;

        const formattedCorrectAnswer = this.formatAnswerForDisplay(question.answer, this.selectedQuestionType);
        const formattedUserAnswer = this.formatAnswerForDisplay(userAnswer, this.selectedQuestionType);

        feedbackElement.innerHTML = `
            <div class="feedback-title">
                ${isCorrect ? '✅ Correct!' : '❌ Incorrect'}
            </div>
            <div>
                ${isCorrect
                    ? `You selected: <strong>${formattedUserAnswer}</strong> ✓`
                    : `You selected: <strong>${formattedUserAnswer}</strong><br>The correct answer is: <strong>${formattedCorrectAnswer}</strong>`
                }
            </div>
            ${question.explanation ? `<div style="margin-top: 0.5rem;"><strong>Explanation:</strong> ${question.explanation}</div>` : ''}
        `;

        feedbackElement.classList.remove('hidden');
    }

    nextQuizQuestion() {
        console.log(`Moving to next question. Current index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}`);

        this.quizState.currentQuestionIndex++;

        if (this.quizState.currentQuestionIndex >= this.currentQuestions.length) {
            console.log('Reached end of quiz, finishing...');
            this.finishQuiz();
            return;
        }

        console.log(`Displaying question ${this.quizState.currentQuestionIndex + 1}`);
        this.displayQuizQuestion();
    }

    async finishQuiz() {
        this.quizState.endTime = new Date();

        // Save quiz session to database
        await this.saveQuizSession();

        this.showQuizResults();
    }

    async saveQuizSession() {
        try {
            const duration = Math.round((this.quizState.endTime - this.quizState.startTime) / 1000);
            const scorePercentage = Math.round((this.quizState.score.correct / this.currentQuestions.length) * 100);

            const session = {
                timestamp: new Date().toISOString(),
                question_type: this.selectedQuestionType, // Use snake_case for database compatibility
                score: {
                    correct: this.quizState.score.correct,
                    total: this.currentQuestions.length
                },
                duration: duration,
                answers: this.quizState.answers,
                questions: this.currentQuestions
            };

            const saveResult = await window.electronAPI.saveQuizSession(session);
            if (saveResult.success) {
                console.log('Quiz session saved successfully');

                // Add activity to live feed
                this.addActivityToFeed({
                    type: 'quiz_completed',
                    questionType: this.selectedQuestionType,
                    score: scorePercentage
                });

                // Check for high score (above 90%)
                if (scorePercentage >= 90) {
                    this.addActivityToFeed({
                        type: 'high_score',
                        score: scorePercentage
                    });
                }
            } else {
                console.warn('Failed to save quiz session:', saveResult.error);
            }
        } catch (error) {
            console.error('Error saving quiz session:', error);
        }
    }

    showQuizResults() {
        this.showScreen('resultsScreen');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const score = this.quizState.score;
        const percentage = Math.round((score.correct / score.total) * 100);
        const duration = Math.round((this.quizState.endTime - this.quizState.startTime) / 1000);

        const resultsDisplay = document.getElementById('resultsDisplay');
        resultsDisplay.innerHTML = `
            <div class="score-summary">
                <div class="score-item">
                    <span class="score-value">${percentage}%</span>
                    <div class="score-label">${isArabic ? 'النتيجة الإجمالية' : 'Overall Score'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.correct}</span>
                    <div class="score-label">${isArabic ? 'الإجابات الصحيحة' : 'Correct Answers'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.total - score.correct}</span>
                    <div class="score-label">${isArabic ? 'الإجابات الخاطئة' : 'Incorrect Answers'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${duration}s</span>
                    <div class="score-label">${isArabic ? 'الوقت المستغرق' : 'Time Taken'}</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <h3>${isArabic ? 'الأداء' : 'Performance'}: ${this.getPerformanceLevel(percentage)}</h3>
                <p style="color: #666; margin-top: 0.5rem;">
                    ${this.getPerformanceMessage(percentage)}
                </p>
            </div>
        `;

    }

    getPerformanceLevel(percentage) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        if (percentage >= 90) return isArabic ? 'ممتاز! 🏆' : 'Excellent! 🏆';
        if (percentage >= 80) return isArabic ? 'جيد جداً! 🌟' : 'Very Good! 🌟';
        if (percentage >= 70) return isArabic ? 'جيد! 👍' : 'Good! 👍';
        if (percentage >= 60) return isArabic ? 'مقبول 📚' : 'Fair 📚';
        return isArabic ? 'يحتاج تحسين 💪' : 'Needs Improvement 💪';
    }

    getPerformanceMessage(percentage) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        if (percentage >= 90) {
            return isArabic
                ? 'أداء متميز! لقد أتقنت هذا الموضوع.'
                : 'Outstanding performance! You have mastered this topic.';
        }
        if (percentage >= 80) {
            return isArabic
                ? 'عمل رائع! لديك فهم قوي للمادة.'
                : 'Great job! You have a strong understanding of the material.';
        }
        if (percentage >= 70) {
            return isArabic
                ? 'أحسنت! فكر في مراجعة المواضيع التي أخطأت فيها.'
                : 'Well done! Consider reviewing the topics you missed.';
        }
        if (percentage >= 60) {
            return isArabic
                ? 'أنت على الطريق الصحيح. المزيد من الممارسة سيساعد في تحسين نتيجتك.'
                : 'You\'re on the right track. More practice will help improve your score.';
        }
        return isArabic
            ? 'استمر في الدراسة! راجع المادة وحاول مرة أخرى لتحسين فهمك.'
            : 'Keep studying! Review the material and try again to improve your understanding.';
    }

    handleFileSelect(file) {
        if (!file) return;

        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const generateBtn = document.getElementById('generateFromFile');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        fileInfo.classList.remove('hidden');
        generateBtn.disabled = false;
    }

    handleImageSelect(file) {
        if (!file) return;

        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const generateBtn = document.getElementById('generateFromImage');

        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            imagePreview.classList.remove('hidden');
            generateBtn.disabled = false;
        };
        reader.readAsDataURL(file);
    }

    removeFile() {
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('generateFromFile').disabled = true;
    }

    removeImage() {
        document.getElementById('imageInput').value = '';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('generateFromImage').disabled = true;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Placeholder methods for future implementation

    showAbout() {
        // Show about information
        const aboutContent = `
            <div style="text-align: center; padding: 20px;">
                <h2>MCQ & TF Question Generator</h2>
                <p>Version 1.0.0</p>
                <p>Desktop application for generating multiple choice and true/false questions from documents and images.</p>
                <p>Built with Electron and powered by AI.</p>
            </div>
        `;

        // Create a simple modal or use existing notification system
        this.showNotification('MCQ & TF Generator v1.0.0 - AI-powered question generation', 'info');
    }

    showHelp() {
        // Show help information
        const helpContent = `
            <div>
                <h3>How to use:</h3>
                <ul>
                    <li>Upload a PDF file or image</li>
                    <li>Select question type (MCQ or True/False)</li>
                    <li>Set number of questions per page</li>
                    <li>Click "Start Quiz" to begin</li>
                    <li>View your history and statistics</li>
                </ul>
            </div>
        `;

        this.showNotification('Check the main interface for easy-to-use controls and navigation', 'info');
    }

    reviewAnswers() {
        this.showScreen('questionsScreen');
    }

    showSaveQuizNameDialog(validQuestions) {
        const dialog = document.getElementById('saveQuizNameDialog');
        const customQuizName = document.getElementById('customQuizName');
        const previewQuestionType = document.getElementById('previewQuestionType');
        const previewQuestionCount = document.getElementById('previewQuestionCount');
        const previewSource = document.getElementById('previewSource');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Set up preview information
        previewQuestionType.textContent = this.selectedQuestionType;
        previewQuestionCount.textContent = validQuestions.length;
        previewSource.textContent = this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content');

        // Clear previous input and set placeholder
        customQuizName.value = '';
        customQuizName.placeholder = isArabic ? 'أدخل اسم الاختبار...' : 'Enter quiz name...';

        // Store valid questions for later use
        this.pendingQuizQuestions = validQuestions;

        // Show dialog
        dialog.style.display = 'flex';
        customQuizName.focus();

        // Set up event listeners if not already set
        this.setupSaveQuizDialogListeners();
    }

    setupSaveQuizDialogListeners() {
        const dialog = document.getElementById('saveQuizNameDialog');
        const customQuizName = document.getElementById('customQuizName');
        const confirmBtn = document.getElementById('confirmSaveQuiz');
        const cancelBtn = document.getElementById('cancelSaveQuiz');
        const closeBtn = document.getElementById('closeSaveQuizNameDialog');

        // Remove existing listeners to prevent duplicates
        confirmBtn.replaceWith(confirmBtn.cloneNode(true));
        cancelBtn.replaceWith(cancelBtn.cloneNode(true));
        closeBtn.replaceWith(closeBtn.cloneNode(true));

        // Get fresh references after cloning
        const newConfirmBtn = document.getElementById('confirmSaveQuiz');
        const newCancelBtn = document.getElementById('cancelSaveQuiz');
        const newCloseBtn = document.getElementById('closeSaveQuizNameDialog');

        // Confirm save
        newConfirmBtn.addEventListener('click', () => {
            const customName = customQuizName.value.trim();
            this.confirmSaveQuiz(customName);
        });

        // Cancel/Close
        const closeDialog = () => {
            dialog.style.display = 'none';
            this.pendingQuizQuestions = null;
        };

        newCancelBtn.addEventListener('click', closeDialog);
        newCloseBtn.addEventListener('click', closeDialog);

        // Handle Enter key in input
        customQuizName.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const customName = customQuizName.value.trim();
                this.confirmSaveQuiz(customName);
            }
        });

        // Handle Escape key
        dialog.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    }

    async confirmSaveQuiz(customName) {
        try {
            const dialog = document.getElementById('saveQuizNameDialog');
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            if (!this.pendingQuizQuestions) {
                this.showNotification(
                    isArabic ? 'خطأ: لا توجد أسئلة للحفظ' : 'Error: No questions to save',
                    'error'
                );
                return;
            }

            // Create a saved quiz object with explicit Gregorian date formatting
            const currentDate = new Date();
            const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
            const day = currentDate.getDate().toString().padStart(2, '0');
            const year = currentDate.getFullYear();
            const formattedDate = `${month}/${day}/${year}`;

            // Use custom name if provided, otherwise use default
            let quizTitle;
            if (customName) {
                quizTitle = customName;
            } else {
                quizTitle = isArabic ?
                    `اختبار محفوظ - ${this.selectedQuestionType} - ${formattedDate}` :
                    `Saved Quiz - ${this.selectedQuestionType} - ${formattedDate}`;
            }

            const savedQuiz = {
                id: Date.now().toString(),
                title: quizTitle,
                questionType: this.selectedQuestionType,
                questions: this.pendingQuizQuestions,
                createdAt: currentDate.toISOString(),
                source: this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content')
            };

            console.log('Saving quiz:', savedQuiz);

            // Close dialog first
            dialog.style.display = 'none';
            this.pendingQuizQuestions = null;

            // Save to database
            const result = await window.electronAPI.saveSavedQuiz(savedQuiz);

            console.log('Save result:', result);

            if (result && result.success) {
                const message = isArabic ?
                    'تم حفظ الاختبار بنجاح! يمكنك العثور عليه في صفحة التاريخ.' :
                    'Quiz saved successfully! You can find it in the history page.';
                this.showNotification(message, 'success');

                // If we're currently on the history page, refresh the saved quizzes list
                const currentScreen = document.querySelector('.screen:not([style*="display: none"])');
                if (currentScreen && currentScreen.id === 'historyScreen') {
                    console.log('Refreshing saved quizzes list after save...');
                    await this.loadSavedQuizzes();
                }
            } else {
                const errorMsg = result?.error || 'Unknown error';
                console.error('Save failed:', errorMsg);
                const message = isArabic ?
                    `فشل في حفظ الاختبار: ${errorMsg}` :
                    `Failed to save quiz: ${errorMsg}`;
                this.showNotification(message, 'error');
            }
        } catch (error) {
            console.error('Error confirming save quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    async saveQuizForLater() {
        try {
            if (!this.currentQuestions || this.currentQuestions.length === 0) {
                const currentLanguage = localStorage.getItem('language') || 'en';
                const isArabic = currentLanguage === 'ar';
                const message = isArabic ? 'لا توجد أسئلة اختبار للحفظ' : 'No quiz questions to save';
                this.showNotification(message, 'warning');
                return;
            }

            // Get current language for user-friendly messages
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            // Validate questions have required fields
            const validQuestions = this.currentQuestions.filter(q => q.question && q.answer);
            if (validQuestions.length === 0) {
                const message = isArabic ? 'الأسئلة غير صالحة للحفظ' : 'Questions are not valid for saving';
                this.showNotification(message, 'warning');
                return;
            }

            // Show the inline save quiz form
            this.showInlineSaveQuizForm(validQuestions);
        } catch (error) {
            console.error('Error saving quiz for later:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    showInlineSaveQuizForm(validQuestions) {
        const form = document.getElementById('inlineSaveQuizForm');
        const nameInput = document.getElementById('inlineQuizName');
        const previewType = document.getElementById('inlinePreviewType');
        const previewCount = document.getElementById('inlinePreviewCount');
        const previewSource = document.getElementById('inlinePreviewSource');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Set up preview information
        previewType.textContent = this.selectedQuestionType;
        previewCount.textContent = validQuestions.length;
        previewSource.textContent = this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content');

        // Clear previous input and set placeholder
        nameInput.value = '';
        nameInput.placeholder = isArabic ? 'أدخل اسم الاختبار...' : 'Enter quiz name...';

        // Store valid questions for later use
        this.pendingQuizQuestions = validQuestions;

        // Show form with animation
        form.style.display = 'block';
        setTimeout(() => {
            form.classList.add('show');
        }, 10);

        // Focus on input
        setTimeout(() => {
            nameInput.focus();
        }, 400);

        // Set up event listeners
        this.setupInlineSaveFormListeners();
    }

    setupInlineSaveFormListeners() {
        const form = document.getElementById('inlineSaveQuizForm');
        const nameInput = document.getElementById('inlineQuizName');
        const confirmBtn = document.getElementById('confirmInlineSave');
        const cancelBtn = document.getElementById('cancelInlineSave');

        // Remove existing listeners to prevent duplicates
        confirmBtn.replaceWith(confirmBtn.cloneNode(true));
        cancelBtn.replaceWith(cancelBtn.cloneNode(true));

        // Get fresh references after cloning
        const newConfirmBtn = document.getElementById('confirmInlineSave');
        const newCancelBtn = document.getElementById('cancelInlineSave');

        // Confirm save
        newConfirmBtn.addEventListener('click', () => {
            const customName = nameInput.value.trim();
            this.confirmInlineSaveQuiz(customName);
        });

        // Cancel
        const hideForm = () => {
            form.classList.remove('show');
            setTimeout(() => {
                form.style.display = 'none';
                this.pendingQuizQuestions = null;
            }, 400);
        };

        newCancelBtn.addEventListener('click', hideForm);

        // Handle Enter key in input
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const customName = nameInput.value.trim();
                this.confirmInlineSaveQuiz(customName);
            }
        });

        // Handle Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && form.classList.contains('show')) {
                hideForm();
            }
        });
    }

    async confirmInlineSaveQuiz(customName) {
        try {
            const form = document.getElementById('inlineSaveQuizForm');
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            if (!this.pendingQuizQuestions) {
                this.showNotification(
                    isArabic ? 'خطأ: لا توجد أسئلة للحفظ' : 'Error: No questions to save',
                    'error'
                );
                return;
            }

            // Generate quiz title
            const currentDate = new Date();
            const defaultTitle = isArabic ?
                `اختبار ${this.selectedQuestionType} - ${currentDate.toLocaleDateString('ar-SA')}` :
                `${this.selectedQuestionType} Quiz - ${currentDate.toLocaleDateString('en-US')}`;

            const quizTitle = customName || defaultTitle;

            const savedQuiz = {
                id: Date.now().toString(),
                title: quizTitle,
                questionType: this.selectedQuestionType,
                questions: this.pendingQuizQuestions,
                createdAt: currentDate.toISOString(),
                source: this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content')
            };

            console.log('Saving quiz:', savedQuiz);

            // Hide form first
            form.classList.remove('show');
            setTimeout(() => {
                form.style.display = 'none';
                this.pendingQuizQuestions = null;
            }, 400);

            // Save to database
            const result = await window.electronAPI.saveSavedQuiz(savedQuiz);

            console.log('Save result:', result);

            if (result.success) {
                const successMessage = isArabic ?
                    `تم حفظ الاختبار "${quizTitle}" بنجاح` :
                    `Quiz "${quizTitle}" saved successfully`;
                this.showNotification(successMessage, 'success');
            } else {
                const errorMessage = isArabic ?
                    `فشل في حفظ الاختبار: ${result.error}` :
                    `Failed to save quiz: ${result.error}`;
                this.showNotification(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Error confirming save quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    handleExternalFile(filePath) {
        this.showNotification(`File selected: ${filePath}`, 'info');
        // Handle external file selection from menu
    }

    detectUniversalContradictions(question, questionNumber, questionText, explanation, answer) {
        console.log(`🔍 Universal contradiction analysis for question ${questionNumber}`);

        // 1. Exclusivity vs. Plurality Contradictions
        const exclusiveWords = ['solely', 'only', 'exclusively', 'just', 'merely', 'purely', 'simply', 'uniquely'];
        const pluralityWords = ['also', 'additionally', 'furthermore', 'not just', 'not only', 'more than', 'as well as', 'including', 'plus', 'and', 'various', 'multiple', 'several'];

        const hasExclusiveWord = exclusiveWords.some(word => questionText.includes(word));
        const hasPluralityWord = pluralityWords.some(word => explanation.includes(word));

        if (hasExclusiveWord && hasPluralityWord && answer === 'True') {
            console.warn(`⚠️  EXCLUSIVITY CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question claims exclusivity but explanation suggests plurality`);
            console.warn(`   Exclusive words: ${exclusiveWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Plurality words in explanation: ${pluralityWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 2. Absolute vs. Conditional Contradictions
        const absoluteWords = ['always', 'never', 'all', 'none', 'every', 'no', 'completely', 'totally', 'entirely', 'absolutely'];
        const conditionalWords = ['sometimes', 'often', 'usually', 'typically', 'generally', 'most', 'some', 'may', 'might', 'can', 'could', 'depends', 'varies'];

        const hasAbsoluteWord = absoluteWords.some(word => questionText.includes(word));
        const hasConditionalWord = conditionalWords.some(word => explanation.includes(word));

        if (hasAbsoluteWord && hasConditionalWord && answer === 'True') {
            console.warn(`⚠️  ABSOLUTE vs CONDITIONAL CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question uses absolute terms but explanation suggests conditions/exceptions`);
            console.warn(`   Absolute words: ${absoluteWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Conditional words in explanation: ${conditionalWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 3. Scope Limitation vs. Broad Scope Contradictions
        const limitingWords = ['limited to', 'restricted to', 'confined to', 'specific to', 'dedicated to'];
        const broadScopeWords = ['wide range', 'broad spectrum', 'various types', 'multiple areas', 'diverse', 'comprehensive'];

        const hasLimitingWord = limitingWords.some(phrase => questionText.includes(phrase));
        const hasBroadScopeWord = broadScopeWords.some(phrase => explanation.includes(phrase));

        if (hasLimitingWord && hasBroadScopeWord && answer === 'True') {
            console.warn(`⚠️  SCOPE CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question suggests limited scope but explanation indicates broad scope`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        console.log(`✅ No universal contradictions detected in question ${questionNumber}`);
    }



    // Navigation Confirmation
    confirmBackToMain(message) {
        if (confirm(message)) {
            this.showScreen('homeScreen');
            // Reset quiz state if in progress
            if (this.quizState) {
                this.quizState = {
                    currentQuestionIndex: 0,
                    answers: [],
                    score: { correct: 0, total: 0 },
                    startTime: null,
                    endTime: null
                };
            }
            this.showNotification('Returned to main menu', 'info');
        }
    }

    // Image to PDF Converter Methods
    setupImageToPdfListeners() {
        // Image to PDF service launcher
        document.getElementById('imageToPdfService').addEventListener('click', () => {
            this.showScreen('imageToPdfScreen');
        });

        // PDF image drop zone
        const pdfImageDropZone = document.getElementById('pdfImageDropZone');
        const pdfImageInput = document.getElementById('pdfImageInput');

        // Drag and drop functionality
        pdfImageDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.add('dragover');
        });

        pdfImageDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.remove('dragover');
        });

        pdfImageDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleImageFiles(files);
        });

        pdfImageDropZone.addEventListener('click', () => {
            pdfImageInput.click();
        });

        pdfImageInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleImageFiles(files);
        });

        // Settings listeners
        document.getElementById('pdfQuality').addEventListener('change', (e) => {
            this.pdfSettings.quality = e.target.value;
        });

        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pdfSettings.pageSize = e.target.value;
        });

        document.getElementById('orientation').addEventListener('change', (e) => {
            this.pdfSettings.orientation = e.target.value;
        });

        document.getElementById('margin').addEventListener('change', (e) => {
            this.pdfSettings.margin = e.target.value;
        });

        // Action buttons
        document.getElementById('clearAllImages').addEventListener('click', () => {
            this.clearAllImages();
        });

        document.getElementById('convertToPdfBtn').addEventListener('click', () => {
            this.convertImagesToPdf();
        });
    }

    handleImageFiles(files) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
        const validFiles = files.filter(file => {
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            return imageExtensions.includes(extension);
        });

        if (validFiles.length === 0) {
            this.showNotification('Please select valid image files', 'warning');
            return;
        }

        validFiles.forEach(file => {
            if (!this.selectedImages.find(img => img.name === file.name)) {
                const imageData = {
                    file: file,
                    name: file.name,
                    size: file.size,
                    url: URL.createObjectURL(file)
                };
                this.selectedImages.push(imageData);
            }
        });

        this.updateImagesList();
        this.updateConversionInfo();
    }

    updateImagesList() {
        const container = document.getElementById('selectedImagesContainer');
        const imagesList = document.getElementById('imagesList');

        if (this.selectedImages.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        imagesList.innerHTML = '';

        this.selectedImages.forEach((imageData, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            imageItem.innerHTML = `
                <img src="${imageData.url}" alt="${imageData.name}" class="image-preview">
                <div class="image-name">${imageData.name}</div>
                <button class="remove-image" onclick="app.removeImage(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            imagesList.appendChild(imageItem);
        });
    }

    removeImage(index) {
        if (this.selectedImages[index]) {
            URL.revokeObjectURL(this.selectedImages[index].url);
            this.selectedImages.splice(index, 1);
            this.updateImagesList();
            this.updateConversionInfo();
        }
    }

    clearAllImages() {
        this.selectedImages.forEach(imageData => {
            URL.revokeObjectURL(imageData.url);
        });
        this.selectedImages = [];
        this.updateImagesList();
        this.updateConversionInfo();
    }

    updateConversionInfo() {
        const imageCount = document.getElementById('imageCount');
        const estimatedSize = document.getElementById('estimatedSize');
        const convertBtn = document.getElementById('convertToPdfBtn');

        imageCount.textContent = this.selectedImages.length;

        // Estimate PDF size (rough calculation)
        const totalSize = this.selectedImages.reduce((sum, img) => sum + img.size, 0);
        const estimatedMB = (totalSize / (1024 * 1024)).toFixed(1);
        estimatedSize.textContent = `${estimatedMB} MB`;

        // Enable/disable convert button
        convertBtn.disabled = this.selectedImages.length === 0;
    }

    async convertImagesToPdf() {
        if (this.selectedImages.length === 0) {
            this.showNotification('Please select images first', 'warning');
            return;
        }

        try {
            this.showNotification('Converting images to PDF...', 'info');

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save PDF',
                defaultPath: `images_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Generate PDF content
                const pdfContent = await this.generateImagePdfContent();

                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, pdfContent);

                if (saveResult.success) {
                    this.showNotification(`PDF saved successfully to ${result.filePath}`, 'success');
                } else {
                    throw new Error(saveResult.error || 'Failed to save PDF');
                }
            } else if (result.canceled) {
                this.showNotification('Conversion canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('PDF conversion error:', error);
            this.showNotification(`Conversion failed: ${error.message}`, 'error');
        }
    }

    async generateImagePdfContent() {
        // Generate HTML content for PDF with images
        let htmlContent = `
            <html>
            <head>
                <title>Image to PDF</title>
                <style>
                    @page {
                        ${this.getMarginStyles()}
                        size: ${this.pdfSettings.pageSize.toUpperCase()};
                    }
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, sans-serif;
                    }
                    .page {
                        page-break-after: always;
                        page-break-inside: avoid;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 100%;
                        height: 100vh;
                        box-sizing: border-box;
                    }
                    .page:last-child {
                        page-break-after: auto;
                    }
                    .image {
                        max-width: 100%;
                        max-height: 100%;
                        width: auto;
                        height: auto;
                        object-fit: contain;
                        display: block;
                    }
                </style>
            </head>
            <body>
        `;

        // Add each image as a separate page
        for (let i = 0; i < this.selectedImages.length; i++) {
            const imageData = this.selectedImages[i];
            const base64 = await this.fileToBase64(imageData.file);
            const isLastImage = i === this.selectedImages.length - 1;

            htmlContent += `
                <div class="page"${isLastImage ? ' style="page-break-after: avoid;"' : ''}>
                    <img src="${base64}" alt="${imageData.name}" class="image">
                </div>
            `;
        }

        htmlContent += `
            </body>
            </html>
        `;

        return htmlContent;
    }

    getMarginStyles() {
        const margins = {
            none: 'margin: 0;',
            small: 'margin: 10mm;',
            medium: 'margin: 20mm;',
            large: 'margin: 30mm;'
        };
        return margins[this.pdfSettings.margin] || margins.small;
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    fileToArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsArrayBuffer(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    fileToText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsText(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }






    // 3D Anatomy Explorer Methods
    setupAnatomyExplorerListeners() {
        // Anatomy Explorer service launcher
        document.getElementById('anatomyExplorerService').addEventListener('click', () => {
            this.showAnatomyExplorer();
        });

        // Back to home button
        document.getElementById('backToHomeFromAnatomy')?.addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Initialize anatomy data
        this.initializeAnatomyData();
    }

    showAnatomyExplorer() {
        this.showScreen('anatomyExplorerScreen');
        this.initializeAnatomyViewer();
    }

    initializeAnatomyData() {
        // Comprehensive anatomy data structure
        this.anatomyData = {
            bodyParts: {
                // Head and Neck
                brain: {
                    name: 'Brain',
                    system: 'nervous',
                    description: 'The control center of the nervous system, responsible for thought, memory, emotion, and coordination.',
                    function: 'Controls all body functions, processes information, and enables consciousness.',
                    location: 'Inside the skull, protected by the cranium',
                    facts: [
                        'Contains approximately 86 billion neurons',
                        'Uses about 20% of the body\'s energy',
                        'Weighs about 3 pounds (1.4 kg)',
                        'Has no pain receptors'
                    ]
                },
                skull: {
                    name: 'Skull',
                    system: 'skeletal',
                    description: 'The bony structure that protects the brain and forms the head.',
                    function: 'Protects the brain and supports facial structures.',
                    location: 'Forms the head structure',
                    facts: [
                        'Made up of 22 bones',
                        'Cranium protects the brain',
                        'Facial bones support facial features',
                        'Contains openings for eyes, nose, and mouth'
                    ]
                },
                eyes: {
                    name: 'Eyes',
                    system: 'nervous',
                    description: 'Complex organs that detect light and convert it into electrical signals for the brain.',
                    function: 'Vision and light detection.',
                    location: 'Eye sockets (orbits) in the skull',
                    facts: [
                        'Can distinguish about 10 million colors',
                        'Blink about 15-20 times per minute',
                        'Each eye has about 120 million rods and 6 million cones',
                        'The cornea is the only part of the body with no blood supply'
                    ]
                },

                // Torso - Circulatory System
                heart: {
                    name: 'Heart',
                    system: 'circulatory',
                    description: 'A muscular organ that pumps blood throughout the body via the circulatory system.',
                    function: 'Pumps oxygenated blood to the body and deoxygenated blood to the lungs.',
                    location: 'Center-left of the chest, between the lungs',
                    facts: [
                        'Beats about 100,000 times per day',
                        'Pumps about 2,000 gallons of blood daily',
                        'Has four chambers: two atria and two ventricles',
                        'The heart muscle never gets tired'
                    ]
                },

                // Respiratory System
                lungs: {
                    name: 'Lungs',
                    system: 'respiratory',
                    description: 'Paired organs that facilitate gas exchange between air and blood.',
                    function: 'Oxygenate blood and remove carbon dioxide from the body.',
                    location: 'On either side of the heart in the chest cavity',
                    facts: [
                        'Right lung has 3 lobes, left lung has 2 lobes',
                        'Contain about 300-500 million alveoli',
                        'Surface area is about the size of a tennis court',
                        'Process about 11,000 liters of air per day'
                    ]
                },

                // Digestive System
                stomach: {
                    name: 'Stomach',
                    system: 'digestive',
                    description: 'A muscular sac that stores and digests food.',
                    function: 'Breaks down food with acid and enzymes, stores food temporarily.',
                    location: 'Upper left area of the abdomen',
                    facts: [
                        'Can hold up to 1.5 liters of food',
                        'Produces about 2-3 liters of gastric juice daily',
                        'pH level is between 1.5-2.0 (very acidic)',
                        'Completely replaces its lining every 3-5 days'
                    ]
                },
                liver: {
                    name: 'Liver',
                    system: 'digestive',
                    description: 'The largest internal organ, performing over 500 vital functions.',
                    function: 'Detoxification, protein synthesis, bile production, metabolism.',
                    location: 'Upper right area of the abdomen',
                    facts: [
                        'Weighs about 3 pounds (1.4 kg)',
                        'Can regenerate itself',
                        'Processes over 1 liter of blood per minute',
                        'Stores vitamins A, D, E, K, and B12'
                    ]
                },

                // Skeletal System
                spine: {
                    name: 'Spine',
                    system: 'skeletal',
                    description: 'The vertebral column that supports the body and protects the spinal cord.',
                    function: 'Structural support, protects spinal cord, enables movement.',
                    location: 'Runs from the skull to the pelvis along the back',
                    facts: [
                        'Made up of 33 vertebrae',
                        'Has 4 natural curves',
                        'Contains 31 pairs of spinal nerves',
                        'Can compress up to 1 inch during the day'
                    ]
                },
                ribcage: {
                    name: 'Rib Cage',
                    system: 'skeletal',
                    description: 'Bony structure that protects the heart, lungs, and other vital organs.',
                    function: 'Protection of thoracic organs and breathing assistance.',
                    location: 'Surrounds the chest cavity',
                    facts: [
                        'Contains 12 pairs of ribs (24 total)',
                        'True ribs (1-7) connect directly to sternum',
                        'False ribs (8-12) connect indirectly or not at all',
                        'Expands and contracts during breathing'
                    ]
                },

                // Muscular System
                biceps: {
                    name: 'Biceps',
                    system: 'muscular',
                    description: 'Large muscle in the front of the upper arm.',
                    function: 'Flexes the elbow and rotates the forearm.',
                    location: 'Front of the upper arm',
                    facts: [
                        'Has two heads (bi = two)',
                        'Works with triceps in opposition',
                        'Helps with pulling movements',
                        'Can be strengthened through resistance training'
                    ]
                },
                quadriceps: {
                    name: 'Quadriceps',
                    system: 'muscular',
                    description: 'Group of four muscles in the front of the thigh.',
                    function: 'Extends the knee and flexes the hip.',
                    location: 'Front of the thigh',
                    facts: [
                        'Largest muscle group in the body',
                        'Made up of 4 separate muscles',
                        'Essential for walking, running, and jumping',
                        'Helps stabilize the kneecap'
                    ]
                }
            },

            bodySystems: {
                circulatory: {
                    name: 'Circulatory System',
                    description: 'Transports blood, nutrients, oxygen, and waste throughout the body.',
                    organs: ['heart', 'blood vessels', 'blood'],
                    color: '#e74c3c'
                },
                respiratory: {
                    name: 'Respiratory System',
                    description: 'Responsible for gas exchange - taking in oxygen and removing carbon dioxide.',
                    organs: ['lungs', 'trachea', 'bronchi', 'diaphragm'],
                    color: '#3498db'
                },
                digestive: {
                    name: 'Digestive System',
                    description: 'Breaks down food and absorbs nutrients for the body.',
                    organs: ['stomach', 'liver', 'intestines', 'pancreas'],
                    color: '#f39c12'
                },
                nervous: {
                    name: 'Nervous System',
                    description: 'Controls and coordinates body functions through electrical signals.',
                    organs: ['brain', 'spinal cord', 'nerves'],
                    color: '#9b59b6'
                },
                muscular: {
                    name: 'Muscular System',
                    description: 'Enables movement and maintains posture.',
                    organs: ['skeletal muscles', 'smooth muscles', 'cardiac muscle'],
                    color: '#e67e22'
                },
                skeletal: {
                    name: 'Skeletal System',
                    description: 'Provides structure, protects organs, and enables movement.',
                    organs: ['bones', 'joints', 'cartilage'],
                    color: '#95a5a6'
                },
                endocrine: {
                    name: 'Endocrine System',
                    description: 'Produces hormones that regulate body functions.',
                    organs: ['thyroid', 'adrenals', 'pancreas', 'pituitary'],
                    color: '#1abc9c'
                },
                reproductive: {
                    name: 'Reproductive System',
                    description: 'Responsible for producing offspring.',
                    organs: ['ovaries', 'testes', 'uterus'],
                    color: '#e91e63'
                }
            }
        };
    }

    initializeAnatomyViewer() {
        // Initialize the 3D viewer
        this.setupAnatomyControls();
        this.populateBodyParts();
        this.setupAnatomyInteractions();

        // Simulate 3D model loading
        setTimeout(() => {
            this.loadAnatomyModel();
        }, 1000);
    }

    setupAnatomyControls() {
        // View mode buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.changeViewMode(btn.dataset.view);
            });
        });

        // System selector
        document.getElementById('systemSelect').addEventListener('change', (e) => {
            this.filterBySystem(e.target.value);
        });

        // Control buttons
        document.getElementById('resetViewBtn')?.addEventListener('click', () => {
            this.resetView();
        });

        document.getElementById('fullscreenBtn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Quick action buttons
        document.getElementById('generateQuizBtn')?.addEventListener('click', () => {
            this.generateAnatomyQuiz();
        });

        document.getElementById('takeNotesBtn')?.addEventListener('click', () => {
            this.openNotesPanel();
        });

        document.getElementById('bookmarkBtn')?.addEventListener('click', () => {
            this.bookmarkCurrentView();
        });

        // Search functionality
        document.getElementById('partSearchInput')?.addEventListener('input', (e) => {
            this.searchBodyParts(e.target.value);
        });
    }

    populateBodyParts() {
        const partsGrid = document.getElementById('bodyPartsGrid');
        if (!partsGrid) return;

        partsGrid.innerHTML = '';

        Object.entries(this.anatomyData.bodyParts).forEach(([key, part]) => {
            const partItem = document.createElement('div');
            partItem.className = 'part-item';
            partItem.dataset.partId = key;
            partItem.innerHTML = `
                <div class="part-icon">
                    <i class="fas fa-circle" style="color: ${this.anatomyData.bodySystems[part.system]?.color || '#fff'}"></i>
                </div>
                <div class="part-name">${part.name}</div>
            `;

            partItem.addEventListener('click', () => {
                this.selectBodyPart(key);
            });

            partsGrid.appendChild(partItem);
        });
    }

    setupAnatomyInteractions() {
        // Simulate 3D model interactions
        const canvas = document.getElementById('anatomyCanvas');
        if (!canvas) return;

        canvas.addEventListener('click', (e) => {
            // Simulate clicking on 3D model parts
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Simple hit detection simulation
            this.handleCanvasClick(x, y);
        });
    }

    loadAnatomyModel() {
        const canvas = document.getElementById('anatomyCanvas');
        if (!canvas) {
            console.error('Anatomy canvas not found');
            return;
        }

        console.log('Loading anatomy model...');

        // Show loading message with bright colors to make it visible
        canvas.innerHTML = `
            <div style="color: white; text-align: center; padding: 50px; background: rgba(76, 175, 80, 0.3); border: 2px solid #4CAF50; border-radius: 10px; margin: 20px;">
                <h2 style="color: #4CAF50; font-size: 24px;">🎯 3D ANATOMY VIEWER</h2>
                <h3>Loading 3D Anatomy Model...</h3>
                <p>Initializing Three.js...</p>
                <p style="color: #FFD700;">📍 You found the 3D viewer area!</p>
            </div>
        `;

        // Wait a moment for DOM to update, then check Three.js
        setTimeout(() => {
            if (typeof THREE === 'undefined') {
                console.error('Three.js not loaded');
                canvas.innerHTML = `
                    <div style="color: white; text-align: center; padding: 50px;">
                        <h3>3D Engine Not Available</h3>
                        <p>Three.js library failed to load</p>
                        <button onclick="window.app.loadAnatomyModel()" style="margin-top: 10px; padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
                    </div>
                `;
                return;
            }

            console.log('Three.js loaded successfully, version:', THREE.REVISION);

            try {
                // Clear loading content
                canvas.innerHTML = '';

                // Initialize Three.js scene
                this.initThreeJS(canvas);
                this.createSimpleTestScene(); // Start with a simple test
                this.animate();
                console.log('3D scene loaded successfully');

                // After 2 seconds, load the actual anatomy models
                setTimeout(() => {
                    this.createAnatomyModels();
                    console.log('Anatomy models loaded');
                }, 2000);

            } catch (error) {
                console.error('Error loading 3D scene:', error);
                canvas.innerHTML = `
                    <div style="color: white; text-align: center; padding: 50px;">
                        <h3>3D Loading Error</h3>
                        <p>Error: ${error.message}</p>
                        <button onclick="window.app.loadAnatomyModel()" style="margin-top: 10px; padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
                    </div>
                `;
            }
        }, 1000);
    }



    initThreeJS(container) {
        console.log('Initializing Three.js scene...');
        console.log('Container dimensions:', container.clientWidth, 'x', container.clientHeight);

        // Ensure container has dimensions
        if (container.clientWidth === 0 || container.clientHeight === 0) {
            container.style.width = '100%';
            container.style.height = '500px';
        }

        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a2e);
        console.log('Scene created');

        // Camera setup
        const width = container.clientWidth || 800;
        const height = container.clientHeight || 600;
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(0, 0, 8);
        console.log('Camera created');

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(width, height);
        this.renderer.setClearColor(0x1a1a2e, 1);
        container.appendChild(this.renderer.domElement);
        console.log('Renderer created and added to DOM');

        // Controls setup (check if OrbitControls is available)
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.enableZoom = true;
            this.controls.enablePan = true;
            console.log('OrbitControls initialized');
        } else {
            console.warn('OrbitControls not available, using basic mouse controls');
        }

        // Simple lighting setup
        const ambientLight = new THREE.AmbientLight(0x404040, 1.2);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(5, 5, 5);
        this.scene.add(directionalLight);

        const pointLight = new THREE.PointLight(0x4CAF50, 0.8, 100);
        pointLight.position.set(-5, 5, 5);
        this.scene.add(pointLight);
        console.log('Lighting setup complete');

        // Raycaster for mouse interactions
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        // Add mouse event listeners
        this.renderer.domElement.addEventListener('click', (event) => {
            this.onMouseClick(event);
        });

        this.renderer.domElement.addEventListener('mousemove', (event) => {
            this.onMouseMove(event);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });

        // Store interactive objects
        this.interactiveObjects = [];
        this.currentViewMode = 'skin';

        console.log('Three.js initialization complete');
    }

    createSimpleTestScene() {
        console.log('Creating simple test scene...');

        // Initialize GLTF loader
        this.gltfLoader = new THREE.GLTFLoader();

        // Try to load a real anatomy model first
        this.loadAnatomyGLTFModels();

        console.log('GLTF loader initialized');
    }

    loadAnatomyGLTFModels() {
        console.log('Attempting to load real anatomy models...');

        // Check if GLB models exist in src/models directory
        const modelFiles = [
            { file: 'src/models/heart.glb', name: 'Heart', position: [-0.2, 1.2, 0.1], scale: 0.1 },
            { file: 'src/models/brain.glb', name: 'Brain', position: [0, 3.2, 0], scale: 0.05 },
            { file: 'src/models/skeleton.glb', name: 'Skeleton', position: [0, 0, 0], scale: 0.01 },
            { file: 'src/models/lungs.glb', name: 'Lungs', position: [0, 1.5, -0.2], scale: 0.08 },
            { file: 'src/models/liver.glb', name: 'Liver', position: [0.3, 0.5, 0.1], scale: 0.1 }
        ];

        let loadedModels = 0;
        let totalModels = modelFiles.length;

        // Try to load each GLB model
        modelFiles.forEach(modelInfo => {
            this.gltfLoader.load(
                modelInfo.file,
                (gltf) => {
                    console.log(`✅ Loaded ${modelInfo.name} from ${modelInfo.file}`);

                    const model = gltf.scene;
                    model.scale.setScalar(modelInfo.scale);
                    model.position.set(...modelInfo.position);
                    model.userData = {
                        name: modelInfo.name,
                        type: 'gltf_model',
                        partId: modelInfo.name.toLowerCase()
                    };

                    this.scene.add(model);
                    this.interactiveObjects.push(model);
                    loadedModels++;

                    if (loadedModels === totalModels) {
                        console.log(`🎉 All ${totalModels} GLB models loaded successfully!`);
                        this.showNotification(`Loaded ${totalModels} real anatomy models!`, 'success');
                    }
                },
                (progress) => {
                    console.log(`Loading ${modelInfo.name}: ${(progress.loaded / progress.total * 100).toFixed(1)}%`);
                },
                (error) => {
                    console.warn(`⚠️ Could not load ${modelInfo.name} from ${modelInfo.file}:`, error);
                    loadedModels++;

                    // If all models failed to load, fall back to geometric models
                    if (loadedModels === totalModels && this.interactiveObjects.length === 0) {
                        console.log('📦 No GLB models found, using geometric models...');
                        this.createRealisticAnatomyModels();
                    }
                }
            );
        });

        // If no models start loading after 1 second, use geometric models
        setTimeout(() => {
            if (this.interactiveObjects.length === 0) {
                console.log('📦 No GLB models available, using geometric models...');
                this.createRealisticAnatomyModels();
            }
        }, 1000);
    }

    createRealisticAnatomyModels() {
        console.log('Creating realistic anatomy models...');

        // Create a more realistic human figure
        this.createRealisticHead();
        this.createRealisticTorso();
        this.createRealisticLimbs();
        this.createInternalOrgans();

        console.log('Realistic anatomy models created');
    }

    createAnatomyModels() {
        console.log('Creating anatomy models for mode:', this.currentViewMode);

        // Clear existing models
        this.clearModels();

        // Create models based on current view mode
        switch (this.currentViewMode) {
            case 'skin':
                this.createSkinModel();
                break;
            case 'muscle':
                this.createMuscleModel();
                break;
            case 'skeleton':
                this.createSkeletonModel();
                break;
            case 'organs':
                this.createOrganModels();
                break;
            default:
                this.createSkinModel();
        }

        console.log('Created', this.interactiveObjects.length, 'interactive objects');
    }

    createBodyOutline() {
        // Create a simple human body outline using cylinder instead of capsule for compatibility
        const bodyGeometry = new THREE.CylinderGeometry(0.8, 0.8, 3, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            transparent: true,
            opacity: 0.3,
            wireframe: false
        });

        this.bodyOutline = new THREE.Mesh(bodyGeometry, bodyMaterial);
        this.bodyOutline.position.set(0, 0, 0);
        this.bodyOutline.userData = { name: 'body', type: 'outline' };
        this.scene.add(this.bodyOutline);
        console.log('Body outline created');
    }

    createRealisticHead() {
        console.log('Creating realistic head...');

        // Main head (more oval shape)
        const headGeometry = new THREE.SphereGeometry(0.9, 20, 16);
        headGeometry.scale(1, 1.2, 0.8); // Make it more head-like
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFDBB3,
            shininess: 50,
            transparent: true,
            opacity: 0.9
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.set(0, 3.2, 0);
        head.userData = { name: 'Head', type: 'skin', partId: 'skull' };
        this.scene.add(head);
        this.interactiveObjects.push(head);

        // Eyes
        const eyeGeometry = new THREE.SphereGeometry(0.12, 12, 12);
        const eyeMaterial = new THREE.MeshPhongMaterial({ color: 0x87CEEB });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.25, 3.3, 0.6);
        leftEye.userData = { name: 'Left Eye', type: 'feature', partId: 'eyes' };
        this.scene.add(leftEye);
        this.interactiveObjects.push(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.25, 3.3, 0.6);
        rightEye.userData = { name: 'Right Eye', type: 'feature', partId: 'eyes' };
        this.scene.add(rightEye);
        this.interactiveObjects.push(rightEye);

        // Nose
        const noseGeometry = new THREE.ConeGeometry(0.08, 0.3, 6);
        const noseMaterial = new THREE.MeshPhongMaterial({ color: 0xFFDBB3 });
        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.position.set(0, 3.1, 0.7);
        nose.rotation.x = Math.PI;
        nose.userData = { name: 'Nose', type: 'feature', partId: 'nose' };
        this.scene.add(nose);
        this.interactiveObjects.push(nose);
    }

    createRealisticTorso() {
        console.log('Creating realistic torso...');

        // Main torso (more anatomical shape)
        const torsoGeometry = new THREE.CylinderGeometry(0.9, 1.1, 3, 16);
        const torsoMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFDBB3,
            shininess: 50,
            transparent: true,
            opacity: 0.9
        });
        const torso = new THREE.Mesh(torsoGeometry, torsoMaterial);
        torso.position.set(0, 0.8, 0);
        torso.userData = { name: 'Torso', type: 'skin', partId: 'torso' };
        this.scene.add(torso);
        this.interactiveObjects.push(torso);

        // Chest muscles (pectorals)
        const chestGeometry = new THREE.SphereGeometry(0.4, 12, 12);
        const chestMaterial = new THREE.MeshPhongMaterial({
            color: 0xE6B89C,
            transparent: true,
            opacity: 0.8
        });

        const leftChest = new THREE.Mesh(chestGeometry, chestMaterial);
        leftChest.position.set(-0.4, 1.8, 0.3);
        leftChest.scale.set(1, 0.6, 0.8);
        leftChest.userData = { name: 'Left Pectoral', type: 'muscle', partId: 'pectorals' };
        this.scene.add(leftChest);
        this.interactiveObjects.push(leftChest);

        const rightChest = new THREE.Mesh(chestGeometry, chestMaterial);
        rightChest.position.set(0.4, 1.8, 0.3);
        rightChest.scale.set(1, 0.6, 0.8);
        rightChest.userData = { name: 'Right Pectoral', type: 'muscle', partId: 'pectorals' };
        this.scene.add(rightChest);
        this.interactiveObjects.push(rightChest);
    }

    createRealisticLimbs() {
        console.log('Creating realistic limbs...');

        // Arms with joints
        this.createRealisticArm(-1.8, 'Left');
        this.createRealisticArm(1.8, 'Right');

        // Legs with joints
        this.createRealisticLeg(-0.6, 'Left');
        this.createRealisticLeg(0.6, 'Right');
    }

    createRealisticArm(xPos, side) {
        // Upper arm
        const upperArmGeometry = new THREE.CylinderGeometry(0.18, 0.22, 1.5, 12);
        const armMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFDBB3,
            shininess: 50
        });
        const upperArm = new THREE.Mesh(upperArmGeometry, armMaterial);
        upperArm.position.set(xPos, 1.2, 0);
        upperArm.rotation.z = xPos > 0 ? -Math.PI / 6 : Math.PI / 6;
        upperArm.userData = { name: `${side} Upper Arm`, type: 'skin', partId: 'biceps' };
        this.scene.add(upperArm);
        this.interactiveObjects.push(upperArm);

        // Forearm
        const forearmGeometry = new THREE.CylinderGeometry(0.15, 0.18, 1.3, 12);
        const forearm = new THREE.Mesh(forearmGeometry, armMaterial);
        forearm.position.set(xPos * 1.2, -0.2, 0);
        forearm.rotation.z = xPos > 0 ? -Math.PI / 8 : Math.PI / 8;
        forearm.userData = { name: `${side} Forearm`, type: 'skin', partId: 'forearm' };
        this.scene.add(forearm);
        this.interactiveObjects.push(forearm);

        // Hand
        const handGeometry = new THREE.SphereGeometry(0.15, 12, 12);
        handGeometry.scale(1.2, 0.8, 0.6);
        const hand = new THREE.Mesh(handGeometry, armMaterial);
        hand.position.set(xPos * 1.4, -1.2, 0);
        hand.userData = { name: `${side} Hand`, type: 'skin', partId: 'hand' };
        this.scene.add(hand);
        this.interactiveObjects.push(hand);
    }

    createRealisticLeg(xPos, side) {
        // Thigh
        const thighGeometry = new THREE.CylinderGeometry(0.25, 0.3, 2, 12);
        const legMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFDBB3,
            shininess: 50
        });
        const thigh = new THREE.Mesh(thighGeometry, legMaterial);
        thigh.position.set(xPos, -1.5, 0);
        thigh.userData = { name: `${side} Thigh`, type: 'skin', partId: 'quadriceps' };
        this.scene.add(thigh);
        this.interactiveObjects.push(thigh);

        // Calf
        const calfGeometry = new THREE.CylinderGeometry(0.18, 0.25, 1.8, 12);
        const calf = new THREE.Mesh(calfGeometry, legMaterial);
        calf.position.set(xPos, -3.5, 0);
        calf.userData = { name: `${side} Calf`, type: 'skin', partId: 'calf' };
        this.scene.add(calf);
        this.interactiveObjects.push(calf);

        // Foot
        const footGeometry = new THREE.BoxGeometry(0.3, 0.2, 0.8);
        const foot = new THREE.Mesh(footGeometry, legMaterial);
        foot.position.set(xPos, -4.6, 0.3);
        foot.userData = { name: `${side} Foot`, type: 'skin', partId: 'foot' };
        this.scene.add(foot);
        this.interactiveObjects.push(foot);
    }

    createInternalOrgans() {
        console.log('Creating internal organs...');

        // Heart
        const heartGeometry = new THREE.SphereGeometry(0.25, 12, 12);
        heartGeometry.scale(1.2, 1, 0.8);
        const heartMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF4444,
            transparent: true,
            opacity: 0.8
        });
        const heart = new THREE.Mesh(heartGeometry, heartMaterial);
        heart.position.set(-0.2, 1.2, 0.1);
        heart.userData = { name: 'Heart', type: 'organ', partId: 'heart' };
        this.scene.add(heart);
        this.interactiveObjects.push(heart);

        // Lungs
        const lungGeometry = new THREE.SphereGeometry(0.4, 12, 12);
        lungGeometry.scale(0.8, 1.2, 0.6);
        const lungMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF69B4,
            transparent: true,
            opacity: 0.7
        });

        const leftLung = new THREE.Mesh(lungGeometry, lungMaterial);
        leftLung.position.set(-0.5, 1.5, -0.2);
        leftLung.userData = { name: 'Left Lung', type: 'organ', partId: 'lungs' };
        this.scene.add(leftLung);
        this.interactiveObjects.push(leftLung);

        const rightLung = new THREE.Mesh(lungGeometry, lungMaterial);
        rightLung.position.set(0.5, 1.5, -0.2);
        rightLung.userData = { name: 'Right Lung', type: 'organ', partId: 'lungs' };
        this.scene.add(rightLung);
        this.interactiveObjects.push(rightLung);

        // Liver
        const liverGeometry = new THREE.SphereGeometry(0.5, 12, 12);
        liverGeometry.scale(1.5, 0.8, 1);
        const liverMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            transparent: true,
            opacity: 0.8
        });
        const liver = new THREE.Mesh(liverGeometry, liverMaterial);
        liver.position.set(0.3, 0.5, 0.1);
        liver.userData = { name: 'Liver', type: 'organ', partId: 'liver' };
        this.scene.add(liver);
        this.interactiveObjects.push(liver);

        // Stomach
        const stomachGeometry = new THREE.SphereGeometry(0.3, 12, 12);
        stomachGeometry.scale(1.2, 1.5, 0.8);
        const stomachMaterial = new THREE.MeshPhongMaterial({
            color: 0xDDA0DD,
            transparent: true,
            opacity: 0.8
        });
        const stomach = new THREE.Mesh(stomachGeometry, stomachMaterial);
        stomach.position.set(-0.3, 0.2, 0.1);
        stomach.userData = { name: 'Stomach', type: 'organ', partId: 'stomach' };
        this.scene.add(stomach);
        this.interactiveObjects.push(stomach);
    }

    createSkinModel() {
        console.log('Creating realistic skin model...');
        this.createRealisticAnatomyModels();
        console.log('Realistic skin model created with', this.interactiveObjects.length, 'parts');
    }

    createMuscleModel() {
        // Muscle groups with different colors
        const muscleColors = {
            chest: 0xFF6B6B,
            arms: 0x4ECDC4,
            legs: 0x45B7D1,
            back: 0x96CEB4,
            core: 0xFECA57
        };

        // Chest muscles
        const chestGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.3);
        const chestMaterial = new THREE.MeshPhongMaterial({ color: muscleColors.chest });
        const chest = new THREE.Mesh(chestGeometry, chestMaterial);
        chest.position.set(0, 0.8, 0.2);
        chest.userData = { name: 'chest muscles', type: 'muscle', partId: 'biceps' };
        this.scene.add(chest);
        this.interactiveObjects.push(chest);

        // Arm muscles
        const armMuscleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 1.2, 8);
        const armMuscleMaterial = new THREE.MeshPhongMaterial({ color: muscleColors.arms });

        const leftArmMuscle = new THREE.Mesh(armMuscleGeometry, armMuscleMaterial);
        leftArmMuscle.position.set(-1.1, 0.5, 0);
        leftArmMuscle.rotation.z = Math.PI / 6;
        leftArmMuscle.userData = { name: 'left biceps', type: 'muscle', partId: 'biceps' };
        this.scene.add(leftArmMuscle);
        this.interactiveObjects.push(leftArmMuscle);

        const rightArmMuscle = new THREE.Mesh(armMuscleGeometry, armMuscleMaterial);
        rightArmMuscle.position.set(1.1, 0.5, 0);
        rightArmMuscle.rotation.z = -Math.PI / 6;
        rightArmMuscle.userData = { name: 'right biceps', type: 'muscle', partId: 'biceps' };
        this.scene.add(rightArmMuscle);
        this.interactiveObjects.push(rightArmMuscle);

        // Leg muscles
        const legMuscleGeometry = new THREE.CylinderGeometry(0.25, 0.25, 1.8, 8);
        const legMuscleMaterial = new THREE.MeshPhongMaterial({ color: muscleColors.legs });

        const leftLegMuscle = new THREE.Mesh(legMuscleGeometry, legMuscleMaterial);
        leftLegMuscle.position.set(-0.4, -2.3, 0);
        leftLegMuscle.userData = { name: 'left quadriceps', type: 'muscle', partId: 'quadriceps' };
        this.scene.add(leftLegMuscle);
        this.interactiveObjects.push(leftLegMuscle);

        const rightLegMuscle = new THREE.Mesh(legMuscleGeometry, legMuscleMaterial);
        rightLegMuscle.position.set(0.4, -2.3, 0);
        rightLegMuscle.userData = { name: 'right quadriceps', type: 'muscle', partId: 'quadriceps' };
        this.scene.add(rightLegMuscle);
        this.interactiveObjects.push(rightLegMuscle);

        // Core muscles
        const coreGeometry = new THREE.CylinderGeometry(0.6, 0.8, 1.5, 8);
        const coreMaterial = new THREE.MeshPhongMaterial({ color: muscleColors.core });
        const core = new THREE.Mesh(coreGeometry, coreMaterial);
        core.position.set(0, -0.2, 0);
        core.userData = { name: 'core muscles', type: 'muscle', partId: 'stomach' };
        this.scene.add(core);
        this.interactiveObjects.push(core);
    }

    createSkeletonModel() {
        const boneColor = 0xF5F5DC;

        // Skull
        const skullGeometry = new THREE.SphereGeometry(0.45, 12, 12);
        const skullMaterial = new THREE.MeshPhongMaterial({ color: boneColor });
        const skull = new THREE.Mesh(skullGeometry, skullMaterial);
        skull.position.set(0, 2, 0);
        skull.userData = { name: 'skull', type: 'skeleton', partId: 'skull' };
        this.scene.add(skull);
        this.interactiveObjects.push(skull);

        // Spine
        const spineGeometry = new THREE.CylinderGeometry(0.08, 0.08, 3, 8);
        const spineMaterial = new THREE.MeshPhongMaterial({ color: boneColor });
        const spine = new THREE.Mesh(spineGeometry, spineMaterial);
        spine.position.set(0, 0, -0.2);
        spine.userData = { name: 'spine', type: 'skeleton', partId: 'spine' };
        this.scene.add(spine);
        this.interactiveObjects.push(spine);

        // Rib cage
        const ribGeometry = new THREE.TorusGeometry(0.8, 0.05, 8, 16);
        const ribMaterial = new THREE.MeshPhongMaterial({ color: boneColor });

        for (let i = 0; i < 6; i++) {
            const rib = new THREE.Mesh(ribGeometry, ribMaterial);
            rib.position.set(0, 1.2 - i * 0.3, 0);
            rib.rotation.x = Math.PI / 2;
            rib.scale.set(1, 1 - i * 0.1, 1);
            rib.userData = { name: `rib ${i + 1}`, type: 'skeleton', partId: 'ribcage' };
            this.scene.add(rib);
            this.interactiveObjects.push(rib);
        }

        // Arm bones
        const armBoneGeometry = new THREE.CylinderGeometry(0.06, 0.06, 1.5, 8);
        const armBoneMaterial = new THREE.MeshPhongMaterial({ color: boneColor });

        const leftArmBone = new THREE.Mesh(armBoneGeometry, armBoneMaterial);
        leftArmBone.position.set(-1.2, 0.5, 0);
        leftArmBone.rotation.z = Math.PI / 6;
        leftArmBone.userData = { name: 'left arm bone', type: 'skeleton', partId: 'biceps' };
        this.scene.add(leftArmBone);
        this.interactiveObjects.push(leftArmBone);

        const rightArmBone = new THREE.Mesh(armBoneGeometry, armBoneMaterial);
        rightArmBone.position.set(1.2, 0.5, 0);
        rightArmBone.rotation.z = -Math.PI / 6;
        rightArmBone.userData = { name: 'right arm bone', type: 'skeleton', partId: 'biceps' };
        this.scene.add(rightArmBone);
        this.interactiveObjects.push(rightArmBone);

        // Leg bones
        const legBoneGeometry = new THREE.CylinderGeometry(0.08, 0.08, 2, 8);
        const legBoneMaterial = new THREE.MeshPhongMaterial({ color: boneColor });

        const leftLegBone = new THREE.Mesh(legBoneGeometry, legBoneMaterial);
        leftLegBone.position.set(-0.4, -2.5, 0);
        leftLegBone.userData = { name: 'left leg bone', type: 'skeleton', partId: 'quadriceps' };
        this.scene.add(leftLegBone);
        this.interactiveObjects.push(leftLegBone);

        const rightLegBone = new THREE.Mesh(legBoneGeometry, legBoneMaterial);
        rightLegBone.position.set(0.4, -2.5, 0);
        rightLegBone.userData = { name: 'right leg bone', type: 'skeleton', partId: 'quadriceps' };
        this.scene.add(rightLegBone);
        this.interactiveObjects.push(rightLegBone);
    }

    createOrganModels() {
        // Heart - simplified shape for compatibility
        const heartGeometry = new THREE.SphereGeometry(0.3, 12, 12);
        const heartMaterial = new THREE.MeshPhongMaterial({ color: 0xFF4444 });
        const heart = new THREE.Mesh(heartGeometry, heartMaterial);
        heart.position.set(-0.2, 0.5, 0.1);
        heart.scale.set(1.2, 1, 0.8); // Make it more heart-like
        heart.userData = { name: 'heart', type: 'organ', partId: 'heart' };
        this.scene.add(heart);
        this.interactiveObjects.push(heart);
        console.log('Heart created');

        // Lungs - more realistic shape
        const lungGeometry = new THREE.SphereGeometry(0.4, 10, 10);
        const lungMaterial = new THREE.MeshPhongMaterial({ color: 0xFF69B4, transparent: true, opacity: 0.8 });

        const leftLung = new THREE.Mesh(lungGeometry, lungMaterial);
        leftLung.position.set(-0.6, 0.8, -0.1);
        leftLung.scale.set(0.8, 1.2, 0.6);
        leftLung.userData = { name: 'left lung', type: 'organ', partId: 'lungs' };
        this.scene.add(leftLung);
        this.interactiveObjects.push(leftLung);

        const rightLung = new THREE.Mesh(lungGeometry, lungMaterial);
        rightLung.position.set(0.6, 0.8, -0.1);
        rightLung.scale.set(0.8, 1.2, 0.6);
        rightLung.userData = { name: 'right lung', type: 'organ', partId: 'lungs' };
        this.scene.add(rightLung);
        this.interactiveObjects.push(rightLung);

        // Liver - more realistic shape
        const liverGeometry = new THREE.BoxGeometry(0.8, 0.5, 0.4);
        const liverMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
        const liver = new THREE.Mesh(liverGeometry, liverMaterial);
        liver.position.set(0.3, 0, 0.1);
        liver.rotation.y = 0.2;
        liver.userData = { name: 'liver', type: 'organ', partId: 'liver' };
        this.scene.add(liver);
        this.interactiveObjects.push(liver);

        // Stomach - more realistic shape
        const stomachGeometry = new THREE.SphereGeometry(0.25, 10, 10);
        const stomachMaterial = new THREE.MeshPhongMaterial({ color: 0xFFA500 });
        const stomach = new THREE.Mesh(stomachGeometry, stomachMaterial);
        stomach.position.set(-0.3, -0.3, 0.1);
        stomach.scale.set(1.2, 0.8, 0.8);
        stomach.userData = { name: 'stomach', type: 'organ', partId: 'stomach' };
        this.scene.add(stomach);
        this.interactiveObjects.push(stomach);

        // Brain - more realistic shape with texture
        const brainGeometry = new THREE.SphereGeometry(0.35, 16, 16);
        const brainMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFB6C1,
            transparent: true,
            opacity: 0.9
        });
        const brain = new THREE.Mesh(brainGeometry, brainMaterial);
        brain.position.set(0, 2, 0);
        brain.userData = { name: 'brain', type: 'organ', partId: 'brain' };
        this.scene.add(brain);
        this.interactiveObjects.push(brain);

        // Kidneys
        const kidneyGeometry = new THREE.SphereGeometry(0.15, 8, 8);
        const kidneyMaterial = new THREE.MeshPhongMaterial({ color: 0x8B0000 });

        const leftKidney = new THREE.Mesh(kidneyGeometry, kidneyMaterial);
        leftKidney.position.set(-0.8, -0.5, -0.3);
        leftKidney.scale.set(1, 1.5, 0.8);
        leftKidney.userData = { name: 'left kidney', type: 'organ', partId: 'kidneys' };
        this.scene.add(leftKidney);
        this.interactiveObjects.push(leftKidney);

        const rightKidney = new THREE.Mesh(kidneyGeometry, kidneyMaterial);
        rightKidney.position.set(0.8, -0.5, -0.3);
        rightKidney.scale.set(1, 1.5, 0.8);
        rightKidney.userData = { name: 'right kidney', type: 'organ', partId: 'kidneys' };
        this.scene.add(rightKidney);
        this.interactiveObjects.push(rightKidney);
    }

    createHeartShape() {
        const heartShape = new THREE.Shape();
        const x = 0, y = 0;
        heartShape.moveTo(x + 5, y + 5);
        heartShape.bezierCurveTo(x + 5, y + 5, x + 4, y, x, y);
        heartShape.bezierCurveTo(x - 6, y, x - 6, y + 3.5, x - 6, y + 3.5);
        heartShape.bezierCurveTo(x - 6, y + 5.5, x - 4, y + 7.5, x, y + 10);
        heartShape.bezierCurveTo(x + 4, y + 7.5, x + 6, y + 5.5, x + 6, y + 3.5);
        heartShape.bezierCurveTo(x + 6, y + 3.5, x + 6, y, x + 5, y + 5);
        return heartShape;
    }

    clearModels() {
        // Remove all interactive objects
        this.interactiveObjects.forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) obj.material.dispose();
        });
        this.interactiveObjects = [];

        // Remove test cube if it exists
        if (this.testCube) {
            this.scene.remove(this.testCube);
            if (this.testCube.geometry) this.testCube.geometry.dispose();
            if (this.testCube.material) this.testCube.material.dispose();
            this.testCube = null;
        }

        // Remove body outline if it exists
        if (this.bodyOutline) {
            this.scene.remove(this.bodyOutline);
            if (this.bodyOutline.geometry) this.bodyOutline.geometry.dispose();
            if (this.bodyOutline.material) this.bodyOutline.material.dispose();
            this.bodyOutline = null;
        }

        // Remove any other objects except lights and camera
        const objectsToRemove = [];
        this.scene.traverse((child) => {
            if (child.isMesh && child.userData.type) {
                objectsToRemove.push(child);
            }
        });
        objectsToRemove.forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) obj.material.dispose();
        });
    }

    animate() {
        if (!this.renderer) return;

        requestAnimationFrame(() => this.animate());

        // Update controls
        if (this.controls) {
            this.controls.update();
        }

        // Animate test cube
        if (this.testCube) {
            this.testCube.rotation.x += 0.01;
            this.testCube.rotation.y += 0.01;
        }

        // Animate organs (subtle breathing effect)
        if (this.interactiveObjects) {
            this.interactiveObjects.forEach(obj => {
                if (obj.userData.partId === 'heart') {
                    obj.scale.setScalar(1 + Math.sin(Date.now() * 0.01) * 0.1);
                } else if (obj.userData.partId === 'lungs') {
                    obj.scale.y = 1.2 + Math.sin(Date.now() * 0.005) * 0.1;
                }
            });
        }

        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }

    onMouseClick(event) {
        if (!this.camera || !this.scene) return;

        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.interactiveObjects);

        if (intersects.length > 0) {
            const selectedObject = intersects[0].object;
            const partId = selectedObject.userData.partId;

            if (partId) {
                this.selectBodyPart(partId);
                this.highlightObject(selectedObject);
            }
        }
    }

    onMouseMove(event) {
        if (!this.camera || !this.scene) return;

        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.interactiveObjects);

        // Reset all objects to normal state
        this.interactiveObjects.forEach(obj => {
            if (obj.material.emissive) {
                obj.material.emissive.setHex(0x000000);
            }
        });

        // Highlight hovered object
        if (intersects.length > 0) {
            const hoveredObject = intersects[0].object;
            if (hoveredObject.material.emissive) {
                hoveredObject.material.emissive.setHex(0x444444);
            }
            this.renderer.domElement.style.cursor = 'pointer';
        } else {
            this.renderer.domElement.style.cursor = 'default';
        }
    }

    highlightObject(object) {
        // Reset all objects
        this.interactiveObjects.forEach(obj => {
            if (obj.material.emissive) {
                obj.material.emissive.setHex(0x000000);
            }
        });

        // Highlight selected object
        if (object.material.emissive) {
            object.material.emissive.setHex(0x4CAF50);
        }
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        const canvas = document.getElementById('anatomyCanvas');
        if (!canvas) return;

        this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    }

    changeViewMode(mode) {
        console.log(`Switching to ${mode} view`);
        this.currentViewMode = mode;

        // Update active button
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${mode}"]`).classList.add('active');

        if (this.scene) {
            this.createAnatomyModels();
        }

        this.showNotification(`Switched to ${mode} view`, 'info');
    }

    filterBySystem(system) {
        console.log(`Filtering by ${system} system`);

        // Update the body parts grid
        if (system === 'all') {
            this.populateBodyParts();
        } else {
            const partsGrid = document.getElementById('bodyPartsGrid');
            if (partsGrid) {
                partsGrid.innerHTML = '';

                Object.entries(this.anatomyData.bodyParts)
                    .filter(([key, part]) => part.system === system)
                    .forEach(([key, part]) => {
                        const partItem = document.createElement('div');
                        partItem.className = 'part-item';
                        partItem.dataset.partId = key;
                        partItem.innerHTML = `
                            <div class="part-icon">
                                <i class="fas fa-circle" style="color: ${this.anatomyData.bodySystems[part.system]?.color || '#fff'}"></i>
                            </div>
                            <div class="part-name">${part.name}</div>
                        `;

                        partItem.addEventListener('click', () => {
                            this.selectBodyPart(key);
                        });

                        partsGrid.appendChild(partItem);
                    });
            }
        }

        // Also filter 3D models if they exist
        if (this.interactiveObjects) {
            this.interactiveObjects.forEach(obj => {
                const partId = obj.userData.partId;
                const part = this.anatomyData.bodyParts[partId];

                if (system === 'all') {
                    obj.visible = true;
                    // Reset highlighting
                    if (obj.material.emissive) {
                        obj.material.emissive.setHex(0x000000);
                    }
                } else if (part && part.system === system) {
                    obj.visible = true;
                    // Highlight objects in the selected system
                    if (obj.material.emissive) {
                        obj.material.emissive.setHex(0x222222);
                    }
                } else {
                    obj.visible = false;
                }
            });
        }

        this.showNotification(`Showing ${system === 'all' ? 'all systems' : system + ' system'}`, 'info');
    }

    selectBodyPart(partId) {
        // Update UI
        document.querySelectorAll('.part-item').forEach(item => {
            item.classList.remove('active');
        });

        const selectedItem = document.querySelector(`[data-part-id="${partId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('active');
        }

        // Show part information
        this.showPartInformation(partId);
    }

    showPartInformation(partId) {
        const part = this.anatomyData.bodyParts[partId];
        if (!part) return;

        const infoContent = document.getElementById('anatomyInfoContent');
        const partName = document.getElementById('selectedPartName');

        if (partName) {
            partName.textContent = part.name;
        }

        if (infoContent) {
            const systemInfo = this.anatomyData.bodySystems[part.system];

            infoContent.innerHTML = `
                <div class="part-details">
                    <div class="part-header">
                        <div class="system-badge" style="background: ${systemInfo?.color || '#666'}">
                            ${systemInfo?.name || 'Unknown System'}
                        </div>
                    </div>

                    <div class="part-description">
                        <h4>Description</h4>
                        <p>${part.description}</p>
                    </div>

                    <div class="part-function">
                        <h4>Function</h4>
                        <p>${part.function}</p>
                    </div>

                    <div class="part-location">
                        <h4>Location</h4>
                        <p>${part.location}</p>
                    </div>

                    <div class="part-facts">
                        <h4>Interesting Facts</h4>
                        <ul>
                            ${part.facts.map(fact => `<li>${fact}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    handleCanvasClick(x, y) {
        // This method is now handled by onMouseClick for real 3D interactions
        // Keep for backward compatibility
        console.log('Canvas clicked at:', x, y);
    }

    searchBodyParts(query) {
        if (!query.trim()) {
            this.populateBodyParts();
            return;
        }

        const partsGrid = document.getElementById('bodyPartsGrid');
        if (!partsGrid) return;

        partsGrid.innerHTML = '';

        Object.entries(this.anatomyData.bodyParts)
            .filter(([key, part]) =>
                part.name.toLowerCase().includes(query.toLowerCase()) ||
                part.description.toLowerCase().includes(query.toLowerCase())
            )
            .forEach(([key, part]) => {
                const partItem = document.createElement('div');
                partItem.className = 'part-item';
                partItem.dataset.partId = key;
                partItem.innerHTML = `
                    <div class="part-icon">
                        <i class="fas fa-circle" style="color: ${this.anatomyData.bodySystems[part.system]?.color || '#fff'}"></i>
                    </div>
                    <div class="part-name">${part.name}</div>
                `;

                partItem.addEventListener('click', () => {
                    this.selectBodyPart(key);
                });

                partsGrid.appendChild(partItem);
            });
    }

    resetView() {
        if (this.camera && this.controls) {
            // Reset camera position
            this.camera.position.set(0, 0, 5);
            this.camera.lookAt(0, 0, 0);

            // Reset controls
            this.controls.reset();

            console.log('3D view reset');
            this.showNotification('View reset to default position', 'info');
        } else {
            this.showNotification('3D view not initialized', 'warning');
        }
    }

    toggleFullscreen() {
        const anatomyViewer = document.querySelector('.anatomy-viewer');
        if (!anatomyViewer) return;

        if (!document.fullscreenElement) {
            anatomyViewer.requestFullscreen().then(() => {
                this.showNotification('Entered fullscreen mode', 'info');
                // Resize renderer when entering fullscreen
                setTimeout(() => {
                    if (this.renderer && this.camera) {
                        this.onWindowResize();
                    }
                }, 100);
            }).catch(err => {
                console.error('Error entering fullscreen:', err);
                this.showNotification('Could not enter fullscreen', 'error');
            });
        } else {
            document.exitFullscreen().then(() => {
                this.showNotification('Exited fullscreen mode', 'info');
                // Resize renderer when exiting fullscreen
                setTimeout(() => {
                    if (this.renderer && this.camera) {
                        this.onWindowResize();
                    }
                }, 100);
            });
        }
    }

    generateAnatomyQuiz() {
        this.showNotification('Generating anatomy quiz...', 'info');
        // Here you could integrate with the existing quiz generation system
        setTimeout(() => {
            this.showNotification('Quiz generated! Check the Quiz Generator.', 'success');
        }, 2000);
    }

    openNotesPanel() {
        this.showNotification('Notes panel opened', 'info');
        // Implement notes functionality
    }

    bookmarkCurrentView() {
        this.showNotification('Current view bookmarked', 'success');
        // Implement bookmark functionality
    }

    // Cleanup method for when leaving anatomy screen
    cleanupAnatomyViewer() {
        if (this.renderer) {
            // Remove event listeners
            this.renderer.domElement.removeEventListener('click', this.onMouseClick);
            this.renderer.domElement.removeEventListener('mousemove', this.onMouseMove);

            // Dispose of Three.js resources
            this.clearModels();

            if (this.renderer.domElement.parentNode) {
                this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
            }

            this.renderer.dispose();
            this.renderer = null;
            this.scene = null;
            this.camera = null;
            this.controls = null;
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new QuestionGeneratorApp();
});
