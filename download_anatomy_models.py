#!/usr/bin/env python3
"""
Download real 3D anatomy models for the anatomy viewer
"""

import os
import requests
import json
from pathlib import Path

def create_models_directory():
    """Create the models directory if it doesn't exist"""
    models_dir = Path("src/models")
    models_dir.mkdir(parents=True, exist_ok=True)
    return models_dir

def download_file(url, filename, models_dir):
    """Download a file from URL to the models directory"""
    try:
        print(f"Downloading {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        file_path = models_dir / filename
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ Downloaded {filename} ({file_path.stat().st_size / 1024:.1f} KB)")
        return True
    except Exception as e:
        print(f"❌ Failed to download {filename}: {e}")
        return False

def download_anatomy_models():
    """Download free anatomy models"""
    models_dir = create_models_directory()
    
    # Free anatomy models (these are example URLs - you'll need to get actual download links)
    models = {
        # Example models - replace with actual download URLs from Sketchfab/other sources
        "skeleton.glb": "https://example.com/skeleton.glb",  # Replace with real URL
        "heart.glb": "https://example.com/heart.glb",        # Replace with real URL
        "brain.glb": "https://example.com/brain.glb",        # Replace with real URL
        "lungs.glb": "https://example.com/lungs.glb",        # Replace with real URL
    }
    
    print("🔄 Starting anatomy models download...")
    print(f"📁 Models will be saved to: {models_dir.absolute()}")
    
    success_count = 0
    for filename, url in models.items():
        if download_file(url, filename, models_dir):
            success_count += 1
    
    print(f"\n✅ Downloaded {success_count}/{len(models)} models successfully!")
    
    # Create a manifest file
    manifest = {
        "models": list(models.keys()),
        "total": len(models),
        "downloaded": success_count,
        "directory": str(models_dir)
    }
    
    with open(models_dir / "manifest.json", 'w') as f:
        json.dump(manifest, f, indent=2)
    
    print(f"📋 Created manifest.json")
    return success_count > 0

def get_sketchfab_models():
    """Get free anatomy models from Sketchfab API"""
    print("🔍 Searching Sketchfab for free anatomy models...")
    
    # Sketchfab API endpoint for searching models
    api_url = "https://api.sketchfab.com/v3/models"
    params = {
        "q": "anatomy human",
        "downloadable": "true",
        "sort_by": "-likeCount",
        "count": 10
    }
    
    try:
        response = requests.get(api_url, params=params)
        response.raise_for_status()
        data = response.json()
        
        print(f"Found {len(data['results'])} downloadable anatomy models:")
        for i, model in enumerate(data['results'][:5], 1):
            print(f"{i}. {model['name']} by {model['user']['displayName']}")
            print(f"   👍 {model['likeCount']} likes | 👁 {model['viewCount']} views")
            print(f"   🔗 {model['viewerUrl']}")
            print()
        
        return data['results']
    except Exception as e:
        print(f"❌ Failed to search Sketchfab: {e}")
        return []

def create_model_loader_js():
    """Create a JavaScript file to load the downloaded models"""
    models_dir = Path("src/models")
    js_content = '''
// Auto-generated model loader for anatomy viewer
class AnatomyModelLoader {
    constructor() {
        this.loader = new THREE.GLTFLoader();
        this.models = {};
        this.loadedCount = 0;
        this.totalModels = 0;
    }

    async loadAllModels(scene, onProgress = null) {
        const modelFiles = [
            'skeleton.glb',
            'heart.glb',
            'brain.glb',
            'lungs.glb'
        ];

        this.totalModels = modelFiles.length;
        
        for (const filename of modelFiles) {
            try {
                await this.loadModel(`src/models/${filename}`, filename, scene);
                if (onProgress) {
                    onProgress(this.loadedCount, this.totalModels);
                }
            } catch (error) {
                console.warn(`Failed to load ${filename}:`, error);
            }
        }
        
        return this.models;
    }

    loadModel(path, name, scene) {
        return new Promise((resolve, reject) => {
            this.loader.load(
                path,
                (gltf) => {
                    const model = gltf.scene;
                    model.name = name;
                    
                    // Scale and position the model appropriately
                    this.scaleAndPositionModel(model, name);
                    
                    scene.add(model);
                    this.models[name] = model;
                    this.loadedCount++;
                    
                    console.log(`✅ Loaded ${name}`);
                    resolve(model);
                },
                (progress) => {
                    console.log(`Loading ${name}: ${(progress.loaded / progress.total * 100)}%`);
                },
                (error) => {
                    console.error(`❌ Error loading ${name}:`, error);
                    reject(error);
                }
            );
        });
    }

    scaleAndPositionModel(model, name) {
        // Adjust scale and position based on model type
        switch (name) {
            case 'skeleton.glb':
                model.scale.setScalar(0.01);
                model.position.set(0, 0, 0);
                break;
            case 'heart.glb':
                model.scale.setScalar(0.1);
                model.position.set(-0.2, 1.2, 0.1);
                break;
            case 'brain.glb':
                model.scale.setScalar(0.05);
                model.position.set(0, 3.2, 0);
                break;
            case 'lungs.glb':
                model.scale.setScalar(0.08);
                model.position.set(0, 1.5, -0.2);
                break;
            default:
                model.scale.setScalar(0.01);
        }
    }
}

// Make it globally available
window.AnatomyModelLoader = AnatomyModelLoader;
'''
    
    js_file = Path("src/renderer/anatomy-model-loader.js")
    with open(js_file, 'w') as f:
        f.write(js_content)
    
    print(f"📄 Created {js_file}")

if __name__ == "__main__":
    print("🎯 Anatomy Models Downloader")
    print("=" * 40)
    
    # Create models directory
    models_dir = create_models_directory()
    
    # Search for available models
    models = get_sketchfab_models()
    
    # Create model loader
    create_model_loader_js()
    
    print("\n📋 Next Steps:")
    print("1. Visit the Sketchfab URLs above")
    print("2. Download GLB files manually (free account required)")
    print("3. Place GLB files in src/models/ directory")
    print("4. Update the anatomy viewer to use AnatomyModelLoader")
    print("\n🔧 To use real models, update your anatomy viewer:")
    print("   const loader = new AnatomyModelLoader();")
    print("   await loader.loadAllModels(scene);")
