# 🎯 Download Real Anatomy Models - Step by Step

## Quick Start: Get GLB Models

### Step 1: Create Sketchfab Account
1. Go to https://sketchfab.com/signup
2. Create free account (required for downloads)
3. Verify your email

### Step 2: Download These Specific Models

#### 🫀 **Human Heart**
- **URL**: https://sketchfab.com/3d-models/realistic-human-heart-3f8072336ce94d18b3d0d055a1ece089
- **Download as**: GLB
- **Save as**: `src/models/heart.glb`

#### 🧠 **Human Brain**
- **URL**: https://sketchfab.com/3d-models/human-brain-e073c2590bc24daaa7323f4daa5b7784
- **Download as**: GLB
- **Save as**: `src/models/brain.glb`

#### 🦴 **Human Skeleton**
- **Search**: "human skeleton" on Sketchfab
- **Filter**: Downloadable only
- **Download as**: GLB
- **Save as**: `src/models/skeleton.glb`

#### 🫁 **Lungs**
- **Search**: "human lungs anatomy" on Sketchfab
- **Filter**: Downloadable only
- **Download as**: GLB
- **Save as**: `src/models/lungs.glb`

#### 🍖 **Liver**
- **Search**: "human liver anatomy" on Sketchfab
- **Filter**: Downloadable only
- **Download as**: GLB
- **Save as**: `src/models/liver.glb`

### Step 3: File Structure
```
src/
├── models/
│   ├── heart.glb      ← Download here
│   ├── brain.glb      ← Download here
│   ├── skeleton.glb   ← Download here
│   ├── lungs.glb      ← Download here
│   └── liver.glb      ← Download here
└── renderer/
    └── app.js
```

### Step 4: Test Your Models
1. Restart your application
2. Go to Anatomy Viewer
3. You should see real 3D models instead of shapes!

## Alternative Sources

### Free Sources
1. **NIH 3D Print Exchange**: https://3dprint.nih.gov/collections/anatomy
   - Format: STL (convert to GLB using Blender)
   - Quality: Medical grade
   - License: Public domain

2. **Thingiverse**: https://www.thingiverse.com/search?q=anatomy
   - Format: STL/OBJ
   - Quality: Educational
   - License: Various

3. **Free3D**: https://free3d.com/3d-models/human-anatomy
   - Format: Multiple
   - Quality: Mixed
   - License: Free for education

### Paid Sources (High Quality)
1. **TurboSquid**: https://www.turbosquid.com/Search/3D-Models/anatomy
   - Price: $20-500
   - Format: All formats including GLB
   - Quality: Professional

2. **CGTrader**: https://www.cgtrader.com/3d-models/anatomy
   - Price: $10-300
   - Format: GLB available
   - Quality: High detail

## Format Conversion (If Needed)

### Convert STL/OBJ to GLB using Blender:
1. Install Blender (free): https://www.blender.org/download/
2. Open Blender
3. Delete default cube (X key)
4. Import your model: File → Import → STL/OBJ
5. Export as GLB: File → Export → glTF 2.0 (.glb)

### Online Converters:
- **Aspose**: https://products.aspose.app/3d/conversion/obj-to-glb
- **AnyConv**: https://anyconv.com/obj-to-glb-converter/

## File Size Guidelines

### Recommended Sizes:
- **Heart**: 1-5 MB
- **Brain**: 2-10 MB
- **Skeleton**: 5-20 MB
- **Lungs**: 1-5 MB
- **Liver**: 1-5 MB

### If Files Are Too Large:
1. Use Blender to reduce polygon count
2. Compress textures
3. Remove unnecessary details

## Troubleshooting

### Model Not Loading?
1. Check file path: `src/models/heart.glb`
2. Check file size (under 50MB)
3. Check browser console for errors
4. Try a different model

### Model Too Big/Small?
- Edit the scale value in app.js:
```javascript
{ file: 'src/models/heart.glb', scale: 0.1 } // Adjust this number
```

### Model Wrong Position?
- Edit the position in app.js:
```javascript
{ position: [-0.2, 1.2, 0.1] } // [x, y, z] coordinates
```

## Next Steps

1. Download the 5 models listed above
2. Place them in `src/models/` directory
3. Restart your application
4. Enjoy real 3D anatomy models! 🎉

## Pro Tips

- **Start with heart.glb** - it's usually the smallest and easiest to test
- **Check licenses** - make sure you can use them for your project
- **Keep backups** - save original files before editing
- **Test one by one** - add models gradually to identify issues
